{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/.cxx/Debug/215xg5j3/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/.cxx/Debug/215xg5j3/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}