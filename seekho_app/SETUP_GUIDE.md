# Gumbo Learning App Setup Guide

## Package Name Change Complete ✅

The app package name has been successfully changed from `com.seekho.app.seekho_app` to `com.gumbo.learning`.

### Changes Made:

1. **Android Configuration**:
   - Updated `android/app/build.gradle.kts` with new package name and Firebase dependencies
   - Updated `AndroidManifest.xml` with new app label "Gumbo Learning"
   - Moved `MainActivity.kt` to new package structure: `com/gumbo/learning/`
   - Added Firebase and Google Services plugin configuration

2. **Flutter Configuration**:
   - Updated `pubspec.yaml` with Firebase dependencies
   - Updated `main.dart` with Firebase initialization
   - Updated app constants with new app name
   - Created Firebase options configuration file

3. **API Integration**:
   - Enhanced `AuthRemoteDataSource` with all backend user role APIs
   - Added admin authentication methods
   - Added user profile management methods
   - Added Android-specific configuration endpoints

## Firebase Setup Required 🔥

### Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project named "gumbo-learning"
3. Enable Google Analytics (optional)

### Step 2: Add Android App

1. Click "Add app" → Android
2. Enter package name: `com.gumbo.learning`
3. Enter app nickname: "Gumbo Learning"
4. Add SHA-1 certificate fingerprint (see below)
5. Download `google-services.json`
6. Replace the placeholder file at `android/app/google-services.json`

### Step 3: Get SHA-1 Certificate

For debug builds:
```bash
cd android
./gradlew signingReport
```

For release builds, use your keystore:
```bash
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

### Step 4: Enable Authentication

1. In Firebase Console → Authentication → Sign-in method
2. Enable "Google" sign-in provider
3. Add your app's SHA-1 fingerprint
4. Note down the Web client ID for later use

### Step 5: Update Configuration Files

1. **Update `lib/firebase_options.dart`**:
   - Replace all `YOUR_*` placeholders with actual values from Firebase
   - Get these from Project Settings → General → Your apps

2. **Update `lib/core/constants/app_constants.dart`**:
   - Replace `YOUR_GOOGLE_CLIENT_ID` with Web client ID from Firebase
   - Update `baseUrl` with your backend API URL

3. **Update `android/app/google-services.json`**:
   - Replace with the actual file downloaded from Firebase

## Backend Integration 🔗

The app is configured to integrate with all backend APIs mentioned in `backendreadme.md`:

### Authentication APIs:
- ✅ Google Sign-In (`/auth/android/google`)
- ✅ Admin Login (`/auth/admin/login`)
- ✅ Token Refresh (`/auth/android/refresh`)
- ✅ User Profile (`/auth/me`)
- ✅ Admin Management (`/auth/admin/*`)

### Content APIs:
- ✅ Categories (`/categories`)
- ✅ Topics (`/topics`)
- ✅ Videos (`/videos`)

### Subscription APIs:
- ✅ Plans (`/subscriptions/plans`)
- ✅ Payment (`/subscriptions/*`)

### Admin APIs:
- ✅ Dashboard (`/admin/dashboard`)
- ✅ User Management (`/admin/users`)
- ✅ Content Management (`/admin/*`)

## Next Steps 📋

1. **Set up Firebase project** (follow steps above)
2. **Update configuration files** with actual values
3. **Start backend server** (see `backendreadme.md`)
4. **Test Google Sign-In** functionality
5. **Run the app**: `flutter run`

## Testing Checklist ✅

- [ ] Firebase project created and configured
- [ ] Google Sign-In working
- [ ] Backend API connection established
- [ ] User authentication flow working
- [ ] Admin login functionality (if needed)
- [ ] App builds and runs successfully

## Troubleshooting 🔧

### Common Issues:

1. **Google Sign-In fails**:
   - Check SHA-1 fingerprint is added to Firebase
   - Verify `google-services.json` is correct
   - Ensure Web client ID is set in app constants

2. **API calls fail**:
   - Check backend server is running
   - Verify `baseUrl` in app constants
   - Check network permissions in AndroidManifest.xml

3. **Build errors**:
   - Run `flutter clean && flutter pub get`
   - Check all dependencies are properly installed
   - Verify Android SDK and build tools are updated

## Support 💬

For issues related to:
- **Firebase setup**: Check Firebase documentation
- **Backend APIs**: Refer to `backendreadme.md`
- **Flutter issues**: Run `flutter doctor` for diagnostics
