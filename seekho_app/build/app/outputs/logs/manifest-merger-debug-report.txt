-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:2:5-33:19
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
MERGED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:8:5-16:19
MERGED from [:razorpay_flutter] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/razorpay_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-8:19
MERGED from [:razorpay_flutter] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/razorpay_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-8:19
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:41:5-70:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1bd72eccbea45b274668dd2e45f85a1/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1bd72eccbea45b274668dd2e45f85a1/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8a4d50d4621b2ca5c607627ee5b11e8c/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8a4d50d4621b2ca5c607627ee5b11e8c/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1e5e106528d0e5323c8a49fe685c2102/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1e5e106528d0e5323c8a49fe685c2102/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:23:5-20
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/03db382edb63c57d8de629e444088e69/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/03db382edb63c57d8de629e444088e69/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4124f4d019d60f36ebb64ebe4f308a85/transformed/jetified-play-services-identity-17.0.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4124f4d019d60f36ebb64ebe4f308a85/transformed/jetified-play-services-identity-17.0.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:30:5-36:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a0dd44f6bde9aaae99011f8ea15baea7/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a0dd44f6bde9aaae99011f8ea15baea7/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1d97c503b3a4bd9cc3718f3fd84010a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1d97c503b3a4bd9cc3718f3fd84010a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:1:1-45:12
MERGED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:1:1-45:12
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:2:1-11:12
MERGED from [:flutter_secure_storage] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:razorpay_flutter] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/razorpay_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:fluttertoast] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/fluttertoast/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/video_player_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:checkout:1.6.41] /Users/<USER>/.gradle/caches/8.10.2/transforms/38ff6a1f2091d62ed7b060d61316ee18/transformed/jetified-checkout-1.6.41/AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:2:1-72:12
MERGED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1bd72eccbea45b274668dd2e45f85a1/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8a4d50d4621b2ca5c607627ee5b11e8c/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/04f0f0676f3643357eaa49f81d75cd2e/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1e5e106528d0e5323c8a49fe685c2102/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:15:1-25:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a2be31f5f98c024d17eda5160252de46/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/1b29cdb341ee6ce222a593dfe9e4e8a4/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/03db382edb63c57d8de629e444088e69/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4124f4d019d60f36ebb64ebe4f308a85/transformed/jetified-play-services-identity-17.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:17:1-38:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a0dd44f6bde9aaae99011f8ea15baea7/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/cf3a986fc94dc98dab92da85e4b25558/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/2da29a4db85fa016031a8271d099d5c9/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/16069dfcaf91077f5dfacee6009cf7d6/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/45c277e554b2f642f0039a8bdf70fe37/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d08e828b5953a7666b3f2ff56e0b0d85/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/62b74d7625893290d7b631ab22ccef38/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/784c75164cbdfbd8765b78e0df7f6470/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdb7dd109a5baef195820de030bfc0b8/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9b220ff5472263f76e2e1c28ba809216/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7c9426c9c7b147a6092e55ea6cedebc/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c53d2e9525bf91370b12bc771c004400/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1118da8128d511195e65ca01fd969642/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7d52ea09101bb351c12b3e49b2217222/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b19d15f8d9a13fc8029cba36d8cb5768/transformed/loader-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c860eba32ffd640cf27d7bcf8c58afd/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1051ba3f70c22c6b6fe969df2dc22087/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4fc20009fbb469c189a90fdeb685306f/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e3f2b050ee295e3aa25c0b40346b22e9/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5524283dab257914603f1a116284cfb7/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3c95229e85d86102cdd7c59033387933/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8f536ab661db8c110fa8f555d4045bd/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/132ef60730f2297b730425447219585e/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6e4bb0fcc1e64f8f5b3851cfa642d690/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9ffe4f007f8d2eb5cc686fc6c857ca0/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2c54165b066b4ebdf8a3c03617e1f28b/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1d97c503b3a4bd9cc3718f3fd84010a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/0aae34fef115243d36f37a28491e352a/transformed/exifinterface-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c33255f411e345b8c9153cdc8b772a9/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2191f642bb5d35c54ec69e277330946c/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/214b8070ae4c800f920040867b251ddc/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9195964353cc99544e72471d817d6a71/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/1841dfdc9324175fac5338109cf574de/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:1:11-69
queries
ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:39:5-44:15
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:10:5-39:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:41:13-72
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:41:21-70
data
ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:6:5-66
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:24:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
MERGED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/flutter_secure_storage/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:53
MERGED from [:razorpay_flutter] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/razorpay_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:razorpay_flutter] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/razorpay_flutter/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/fluttertoast/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/fluttertoast/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/google_sign_in_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/video_player_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/video_player_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] /Users/<USER>/.gradle/caches/8.10.2/transforms/38ff6a1f2091d62ed7b060d61316ee18/transformed/jetified-checkout-1.6.41/AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] /Users/<USER>/.gradle/caches/8.10.2/transforms/38ff6a1f2091d62ed7b060d61316ee18/transformed/jetified-checkout-1.6.41/AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:18.1.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1bd72eccbea45b274668dd2e45f85a1/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e1bd72eccbea45b274668dd2e45f85a1/transformed/jetified-integrity-1.2.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8a4d50d4621b2ca5c607627ee5b11e8c/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8a4d50d4621b2ca5c607627ee5b11e8c/transformed/jetified-firebase-auth-interop-20.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/04f0f0676f3643357eaa49f81d75cd2e/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/04f0f0676f3643357eaa49f81d75cd2e/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1e5e106528d0e5323c8a49fe685c2102/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1e5e106528d0e5323c8a49fe685c2102/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a2be31f5f98c024d17eda5160252de46/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a2be31f5f98c024d17eda5160252de46/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/1b29cdb341ee6ce222a593dfe9e4e8a4/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/8.10.2/transforms/1b29cdb341ee6ce222a593dfe9e4e8a4/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/03db382edb63c57d8de629e444088e69/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/03db382edb63c57d8de629e444088e69/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4124f4d019d60f36ebb64ebe4f308a85/transformed/jetified-play-services-identity-17.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4124f4d019d60f36ebb64ebe4f308a85/transformed/jetified-play-services-identity-17.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a0dd44f6bde9aaae99011f8ea15baea7/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a0dd44f6bde9aaae99011f8ea15baea7/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/cf3a986fc94dc98dab92da85e4b25558/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/cf3a986fc94dc98dab92da85e4b25558/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/2da29a4db85fa016031a8271d099d5c9/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/2da29a4db85fa016031a8271d099d5c9/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/16069dfcaf91077f5dfacee6009cf7d6/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/16069dfcaf91077f5dfacee6009cf7d6/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/45c277e554b2f642f0039a8bdf70fe37/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/45c277e554b2f642f0039a8bdf70fe37/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d08e828b5953a7666b3f2ff56e0b0d85/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d08e828b5953a7666b3f2ff56e0b0d85/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/62b74d7625893290d7b631ab22ccef38/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/62b74d7625893290d7b631ab22ccef38/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/784c75164cbdfbd8765b78e0df7f6470/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/784c75164cbdfbd8765b78e0df7f6470/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdb7dd109a5baef195820de030bfc0b8/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/bdb7dd109a5baef195820de030bfc0b8/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9b220ff5472263f76e2e1c28ba809216/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9b220ff5472263f76e2e1c28ba809216/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7c9426c9c7b147a6092e55ea6cedebc/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d7c9426c9c7b147a6092e55ea6cedebc/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c53d2e9525bf91370b12bc771c004400/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c53d2e9525bf91370b12bc771c004400/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1118da8128d511195e65ca01fd969642/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1118da8128d511195e65ca01fd969642/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7d52ea09101bb351c12b3e49b2217222/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/7d52ea09101bb351c12b3e49b2217222/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b19d15f8d9a13fc8029cba36d8cb5768/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b19d15f8d9a13fc8029cba36d8cb5768/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c860eba32ffd640cf27d7bcf8c58afd/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4c860eba32ffd640cf27d7bcf8c58afd/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1051ba3f70c22c6b6fe969df2dc22087/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1051ba3f70c22c6b6fe969df2dc22087/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4fc20009fbb469c189a90fdeb685306f/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4fc20009fbb469c189a90fdeb685306f/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e3f2b050ee295e3aa25c0b40346b22e9/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e3f2b050ee295e3aa25c0b40346b22e9/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5524283dab257914603f1a116284cfb7/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5524283dab257914603f1a116284cfb7/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3c95229e85d86102cdd7c59033387933/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/3c95229e85d86102cdd7c59033387933/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8f536ab661db8c110fa8f555d4045bd/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d8f536ab661db8c110fa8f555d4045bd/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/132ef60730f2297b730425447219585e/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.10.2/transforms/132ef60730f2297b730425447219585e/transformed/jetified-security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6e4bb0fcc1e64f8f5b3851cfa642d690/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/6e4bb0fcc1e64f8f5b3851cfa642d690/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9ffe4f007f8d2eb5cc686fc6c857ca0/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/f9ffe4f007f8d2eb5cc686fc6c857ca0/transformed/jetified-firebase-components-17.1.5/AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2c54165b066b4ebdf8a3c03617e1f28b/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2c54165b066b4ebdf8a3c03617e1f28b/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1d97c503b3a4bd9cc3718f3fd84010a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/d1d97c503b3a4bd9cc3718f3fd84010a/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/0aae34fef115243d36f37a28491e352a/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] /Users/<USER>/.gradle/caches/8.10.2/transforms/0aae34fef115243d36f37a28491e352a/transformed/exifinterface-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c33255f411e345b8c9153cdc8b772a9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1c33255f411e345b8c9153cdc8b772a9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2191f642bb5d35c54ec69e277330946c/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/2191f642bb5d35c54ec69e277330946c/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/214b8070ae4c800f920040867b251ddc/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/214b8070ae4c800f920040867b251ddc/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9195964353cc99544e72471d817d6a71/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9195964353cc99544e72471d817d6a71/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/1841dfdc9324175fac5338109cf574de/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/1841dfdc9324175fac5338109cf574de/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:23:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/784c75164cbdfbd8765b78e0df7f6470/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/784c75164cbdfbd8765b78e0df7f6470/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:13:17-116
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:22-95
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:11:9-17:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:18:9-27:18
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:28:9-30:18
action#android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:21-62
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:37:21-58
activity#com.razorpay.CheckoutActivity
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:44:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:46:13-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:43:13-57
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:52:9-60:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:68:13-61
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:34:13-89
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:33:9-35:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:34:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.gumbo.learning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.gumbo.learning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
