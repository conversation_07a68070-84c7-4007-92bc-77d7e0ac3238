{"logs": [{"outputFile": "com.gumbo.learning.app-mergeDebugResources-33:/values-ja/values-ja.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/res/values-ja/values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,3674", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,3770"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/res/values-ja/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1644", "endColumns": "121", "endOffsets": "1761"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/res/values-ja/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "724,828,962,1082,1188,1320,1440,1545,1766,1900,2001,2134,2253,2373,2493,2553,2612", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "823,957,1077,1183,1315,1435,1540,1639,1895,1996,2129,2248,2368,2488,2548,2607,2678"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/res/values-ja/values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "2683,3383,3478,3579", "endColumns": "92,94,100,94", "endOffsets": "2771,3473,3574,3669"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/res/values-ja/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "64", "endOffsets": "266"}, "to": {"startLines": "41", "startColumns": "4", "startOffsets": "3775", "endColumns": "68", "endOffsets": "3839"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/res/values-ja/values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2776,2841,2901,2966,3028,3102,3161,3241,3318", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "2836,2896,2961,3023,3097,3156,3236,3313,3378"}}]}]}