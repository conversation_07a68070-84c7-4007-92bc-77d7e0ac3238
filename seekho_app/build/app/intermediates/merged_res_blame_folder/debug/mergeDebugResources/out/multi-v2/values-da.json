{"logs": [{"outputFile": "com.gumbo.learning.app-mergeDebugResources-33:/values-da/values-da.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/res/values-da/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,888,1048,1175,1284,1427,1552,1672,1904,2060,2166,2328,2455,2600,2778,2844,2906", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "883,1043,1170,1279,1422,1547,1667,1772,2055,2161,2323,2450,2595,2773,2839,2901,2979"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "2984,3773,3872,3979", "endColumns": "111,98,106,96", "endOffsets": "3091,3867,3974,4071"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/res/values-da/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1777", "endColumns": "126", "endOffsets": "1899"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3096,3168,3230,3294,3363,3440,3514,3614,3705", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "3163,3225,3289,3358,3435,3509,3609,3700,3768"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/res/values-da/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "69", "endOffsets": "271"}, "to": {"startLines": "41", "startColumns": "4", "startOffsets": "4177", "endColumns": "73", "endOffsets": "4246"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,4076", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,4172"}}]}]}