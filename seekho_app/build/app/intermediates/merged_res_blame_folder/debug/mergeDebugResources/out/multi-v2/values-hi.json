{"logs": [{"outputFile": "com.gumbo.learning.app-mergeDebugResources-33:/values-hi/values-hi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/res/values-hi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1819", "endColumns": "145", "endOffsets": "1960"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/res/values-hi/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "808,916,1076,1202,1314,1465,1595,1707,1965,2122,2231,2397,2527,2668,2821,2884,2951", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "911,1071,1197,1309,1460,1590,1702,1814,2117,2226,2392,2522,2663,2816,2879,2946,3034"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/res/values-hi/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "41", "startColumns": "4", "startOffsets": "4331", "endColumns": "75", "endOffsets": "4402"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "3039,3913,4015,4127", "endColumns": "105,101,111,102", "endOffsets": "3140,4010,4122,4225"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,4230", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,4326"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3145,3213,3279,3350,3418,3514,3582,3705,3826", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "3208,3274,3345,3413,3509,3577,3700,3821,3908"}}]}]}