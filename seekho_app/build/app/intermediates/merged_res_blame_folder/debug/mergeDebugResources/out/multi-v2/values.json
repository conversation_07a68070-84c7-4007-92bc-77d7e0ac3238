{"logs": [{"outputFile": "com.gumbo.learning.app-mergeDebugResources-33:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/3c95229e85d86102cdd7c59033387933/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "166", "startColumns": "4", "startOffsets": "8799", "endColumns": "42", "endOffsets": "8837"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/d8f536ab661db8c110fa8f555d4045bd/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "168", "startColumns": "4", "startOffsets": "8902", "endColumns": "53", "endOffsets": "8951"}}, {"source": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/generated/res/processDebugGoogleServices/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,199,281,385,494,614,724", "endColumns": "143,81,103,108,119,109,78", "endOffsets": "194,276,380,489,609,719,798"}, "to": {"startLines": "200,213,214,215,216,217,218", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12203,13208,13290,13394,13503,13623,13733", "endColumns": "143,81,103,108,119,109,78", "endOffsets": "12342,13285,13389,13498,13618,13728,13807"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "64,65,66,67,68,69,70,71,181,182,183,184,185,186,187,188,190,191,192,193,194,195,196,197,198,438,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2352,2442,2522,2612,2702,2782,2863,2943,9802,9907,10088,10213,10320,10500,10623,10739,11009,11197,11302,11483,11608,11783,11931,11994,12056,22663,24400", "endLines": "64,65,66,67,68,69,70,71,181,182,183,184,185,186,187,188,190,191,192,193,194,195,196,197,198,450,528", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2437,2517,2607,2697,2777,2858,2938,3018,9902,10083,10208,10315,10495,10618,10734,10837,11192,11297,11478,11603,11778,11926,11989,12051,12130,22973,24812"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "58,59,60,61,85,86,199,210,211,212", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1972,2030,2096,2159,3914,3985,12135,12993,13060,13139", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2025,2091,2154,2216,3980,4052,12198,13055,13134,13203"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,56,57,62,63,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,172,174,175,176,177,178,179,180,219,245,246,250,251,255,288,289,301,307,336,369,399,432", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,1812,1884,2221,2286,3023,3092,4057,4127,4195,4267,4337,4398,4472,4545,4606,4667,4729,4793,4855,4916,4984,5084,5144,5210,5283,5352,5409,5461,5523,5595,5671,5736,5795,5854,5914,5974,6034,6094,6154,6214,6274,6334,6394,6454,6513,6573,6633,6693,6753,6813,6873,6933,6993,7053,7113,7172,7232,7292,7351,7410,7469,7528,7587,7763,7798,7940,7995,8058,8113,8171,8229,8290,8353,8410,8461,8511,8572,8629,8695,8729,8764,9138,9291,9358,9430,9499,9568,9642,9714,13812,14892,15009,15210,15320,15521,17688,17760,18180,18383,19083,20814,21814,22496", "endLines": "25,56,57,62,63,72,73,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,146,147,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,172,174,175,176,177,178,179,180,219,245,249,250,254,255,288,289,306,316,368,389,431,437", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,1879,1967,2281,2347,3087,3150,4122,4190,4262,4332,4393,4467,4540,4601,4662,4724,4788,4850,4911,4979,5079,5139,5205,5278,5347,5404,5456,5518,5590,5666,5731,5790,5849,5909,5969,6029,6089,6149,6209,6269,6329,6389,6449,6508,6568,6628,6688,6748,6808,6868,6928,6988,7048,7108,7167,7227,7287,7346,7405,7464,7523,7582,7641,7793,7828,7990,8053,8108,8166,8224,8285,8348,8405,8456,8506,8567,8624,8690,8724,8759,8794,9203,9353,9425,9494,9563,9637,9709,9797,13878,15004,15205,15315,15516,15645,17755,17822,18378,18679,20809,21490,22491,22658"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4c860eba32ffd640cf27d7bcf8c58afd/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "169", "startColumns": "4", "startOffsets": "8956", "endColumns": "49", "endOffsets": "9001"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "201,202,203,204,205,206,207,208,209", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12347,12417,12479,12544,12608,12685,12750,12840,12924", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "12412,12474,12539,12603,12680,12745,12835,12919,12988"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/res/values/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "165", "endLines": "62", "endColumns": "20", "endOffsets": "1582"}, "to": {"startLines": "451", "startColumns": "4", "startOffsets": "22978", "endLines": "509", "endColumns": "20", "endOffsets": "24395"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/cf3a986fc94dc98dab92da85e4b25558/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "145,149,170,390,395", "startColumns": "4,4,4,4,4", "startOffsets": "7706,7875,9006,21495,21665", "endLines": "145,149,170,394,398", "endColumns": "56,64,63,24,24", "endOffsets": "7758,7935,9065,21660,21809"}}, {"source": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "238,242", "startColumns": "4,4", "startOffsets": "14542,14723", "endLines": "241,244", "endColumns": "12,12", "endOffsets": "14718,14887"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,94", "endLines": "2,19", "endColumns": "38,12", "endOffsets": "89,673"}, "to": {"startLines": "55,221", "startColumns": "4,4", "startOffsets": "1773,13958", "endLines": "55,237", "endColumns": "38,12", "endOffsets": "1807,14537"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/ffafe5f631203f4924364ba072ee19e0/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "173", "startColumns": "4", "startOffsets": "9208", "endColumns": "82", "endOffsets": "9286"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,144,290,296,529,537,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,7646,17827,18022,24817,25099,25713", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,144,295,300,536,551,567", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,7701,18017,18175,25094,25708,26362"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/1118da8128d511195e65ca01fd969642/transformed/jetified-activity-1.8.1/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "148,167", "startColumns": "4,4", "startOffsets": "7833,8842", "endColumns": "41,59", "endOffsets": "7870,8897"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,17,20,28,31,39,54,73,98", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "172,249,316,384,457,519,585,652,715,779,834,993,1169,1642,1810,2283,3295,3690,4203", "endLines": "4,5,6,7,8,9,10,11,12,13,14,17,27,30,38,51,72,97,170", "endColumns": "76,66,67,72,61,65,66,62,63,54,52,70,8,8,8,8,20,20,20", "endOffsets": "248,315,383,456,518,584,651,714,778,833,886,1063,1641,1809,2282,3190,3689,4202,5978"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82,83,84,220,256,264,267,275,317,568,593", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3155,3236,3307,3379,3456,3522,3592,3663,3730,3798,3857,13883,15650,16127,16299,16776,18684,26367,26884", "endLines": "74,75,76,77,78,79,80,81,82,83,84,220,263,266,274,287,335,592,665", "endColumns": "80,70,71,76,65,69,70,66,67,58,56,74,8,8,8,8,20,20,20", "endOffsets": "3231,3302,3374,3451,3517,3587,3658,3725,3793,3852,3909,13953,16122,16294,16771,17683,19078,26879,28659"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "171,189", "startColumns": "4,4", "startOffsets": "9070,10842", "endColumns": "67,166", "endOffsets": "9133,11004"}}]}]}