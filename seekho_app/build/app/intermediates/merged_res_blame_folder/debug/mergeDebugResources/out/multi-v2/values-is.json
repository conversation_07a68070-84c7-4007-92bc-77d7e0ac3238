{"logs": [{"outputFile": "com.gumbo.learning.app-mergeDebugResources-33:/values-is/values-is.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,3962", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,4058"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/bca4f27036848d324a997969c094e057/transformed/browser-1.4.0/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "2920,3654,3755,3861", "endColumns": "103,100,105,100", "endOffsets": "3019,3750,3856,3957"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/4d049585bc2957632373ec30f6c6457b/transformed/jetified-play-services-wallet-18.1.3/res/values-is/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "41", "startColumns": "4", "startOffsets": "4063", "endColumns": "75", "endOffsets": "4134"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/res/values-is/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1746", "endColumns": "128", "endOffsets": "1870"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/78ff6c8884fea0892664a420c0bc3af4/transformed/jetified-media3-exoplayer-1.4.1/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3091,3150,3209,3275,3351,3414,3503,3585", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "3086,3145,3204,3270,3346,3409,3498,3580,3649"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/res/values-is/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,878,1032,1157,1266,1407,1532,1641,1875,2029,2135,2292,2418,2560,2714,2778,2841", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "873,1027,1152,1261,1402,1527,1636,1741,2024,2130,2287,2413,2555,2709,2773,2836,2915"}}]}]}