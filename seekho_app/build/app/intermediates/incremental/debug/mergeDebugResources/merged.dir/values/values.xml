<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2">
    <attr format="color" name="animationBackgroundColor"/>
    <attr format="enum" name="finishPrimaryWithPlaceholder">
        
        <enum name="always" value="1"/>
        
        <enum name="adjacent" value="2"/>
    </attr>
    <attr format="enum" name="finishPrimaryWithSecondary">
        
        <enum name="never" value="0"/>
        
        <enum name="always" value="1"/>
        
        <enum name="adjacent" value="2"/>
    </attr>
    <attr format="enum" name="finishSecondaryWithPrimary">
        
        <enum name="never" value="0"/>
        
        <enum name="always" value="1"/>
        
        <enum name="adjacent" value="2"/>
    </attr>
    <attr format="reference" name="nestedScrollViewStyle"/>
    <attr format="enum" name="splitLayoutDirection">
        
        <enum name="locale" value="0"/>
        
        <enum name="ltr" value="1"/>
        
        <enum name="rtl" value="2"/>
        
        <enum name="topToBottom" value="3"/>
        
        <enum name="bottomToTop" value="4"/>
    </attr>
    <attr format="float" name="splitMaxAspectRatioInLandscape">
        
        <enum name="alwaysAllow" value="0"/>
        
        <enum name="alwaysDisallow" value="-1"/>
    </attr>
    <attr format="float" name="splitMaxAspectRatioInPortrait">
        
        <enum name="alwaysAllow" value="0"/>
        
        <enum name="alwaysDisallow" value="-1"/>
    </attr>
    <attr format="integer" name="splitMinHeightDp"/>
    <attr format="integer" name="splitMinSmallestWidthDp"/>
    <attr format="integer" name="splitMinWidthDp"/>
    <attr format="float" name="splitRatio"/>
    <attr format="string" name="tag"/>
    <bool name="isTablet">false</bool>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="browser_actions_bg_grey">#F5F5F5</color>
    <color name="browser_actions_divider_color">#1E000000</color>
    <color name="browser_actions_text_color">#DE000000</color>
    <color name="browser_actions_title_color">#646464</color>
    <color name="call_notification_answer_color">#1d873b</color>
    <color name="call_notification_decline_color">#d93025</color>
    <color name="common_google_signin_btn_text_dark_default">@android:color/white</color>
    <color name="common_google_signin_btn_text_dark_disabled">#1F000000</color>
    <color name="common_google_signin_btn_text_dark_focused">@android:color/black</color>
    <color name="common_google_signin_btn_text_dark_pressed">@android:color/white</color>
    <color name="common_google_signin_btn_text_light_default">#90000000</color>
    <color name="common_google_signin_btn_text_light_disabled">#1F000000</color>
    <color name="common_google_signin_btn_text_light_focused">#90000000</color>
    <color name="common_google_signin_btn_text_light_pressed">#DE000000</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="wallet_bright_foreground_disabled_holo_light">#ffb2b2b2</color>
    <color name="wallet_bright_foreground_holo_dark">#fff3f3f3</color>
    <color name="wallet_bright_foreground_holo_light">#ff000000</color>
    <color name="wallet_dim_foreground_disabled_holo_dark">#80bebebe</color>
    <color name="wallet_dim_foreground_holo_dark">#bebebe</color>
    <color name="wallet_highlighted_text_holo_dark">#6633b5e5</color>
    <color name="wallet_highlighted_text_holo_light">#6633b5e5</color>
    <color name="wallet_hint_foreground_holo_dark">#808080</color>
    <color name="wallet_hint_foreground_holo_light">#808080</color>
    <color name="wallet_holo_blue_light">#ff33b5e5</color>
    <color name="wallet_link_text_light">#0000ee</color>
    <dimen name="browser_actions_context_menu_max_width">500dp</dimen>
    <dimen name="browser_actions_context_menu_min_padding">20dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">4dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="androidx_window_activity_scope" type="id"/>
    <item name="fragment_container_view_tag" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="report_drawn" type="id"/>
    <item name="special_effects_controller_view_tag" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_on_apply_window_listener" type="id"/>
    <item name="tag_on_receive_content_listener" type="id"/>
    <item name="tag_on_receive_content_mime_types" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_state_description" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="tag_window_insets_animation_callback" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="title" type="id"/>
    <id name="view_tree_lifecycle_owner"/>
    <id name="view_tree_on_back_pressed_dispatcher_owner"/>
    <id name="view_tree_saved_state_registry_owner"/>
    <id name="view_tree_view_model_store_owner"/>
    <item name="visible_removing_fragment_view_tag" type="id"/>
    <integer name="google_play_services_version">12451000</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="androidx_startup" translatable="false">androidx.startup</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string msgid="2523291102206661146" name="common_google_play_services_enable_button">Enable</string>
    <string msgid="227660514972886228" name="common_google_play_services_enable_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t work unless you enable Google Play services.</string>
    <string msgid="5122002158466380389" name="common_google_play_services_enable_title">Enable Google Play services</string>
    <string msgid="7153882981874058840" name="common_google_play_services_install_button">Install</string>
    <string name="common_google_play_services_install_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run without Google Play services, which are missing from your device.</string>
    <string msgid="7215213145546190223" name="common_google_play_services_install_title">Get Google Play services</string>
    <string name="common_google_play_services_notification_channel_name">Google Play services availability</string>
    <string name="common_google_play_services_notification_ticker">Google Play services error</string>
    <string name="common_google_play_services_unknown_issue"><ns1:g id="app_name">%1$s</ns1:g> is having trouble with Google Play services. Please try again.</string>
    <string name="common_google_play_services_unsupported_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run without Google Play services, which are not supported by your device.</string>
    <string msgid="6556509956452265614" name="common_google_play_services_update_button">Update</string>
    <string msgid="9053896323427875356" name="common_google_play_services_update_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run unless you update Google Play services.</string>
    <string msgid="6006316683626838685" name="common_google_play_services_update_title">Update Google Play services</string>
    <string name="common_google_play_services_updating_text"><ns1:g id="app_name">%1$s</ns1:g> won\'t run without Google Play services, which are currently updating.</string>
    <string name="common_google_play_services_wear_update_text">New version of Google Play services needed. It will update itself shortly.</string>
    <string name="common_open_on_phone">Open on phone</string>
    <string name="common_signin_button_text">Sign in</string>
    <string name="common_signin_button_text_long">Sign in with Google</string>
    <string name="copy_toast_msg">Link copied to clipboard</string>
    <string name="default_web_client_id" translatable="false">601890245278-jbg05oftf76pprvmu52jq1gdjmmjfnbl.apps.googleusercontent.com</string>
    <string name="exo_download_completed">Download completed</string>
    <string name="exo_download_description">Download</string>
    <string name="exo_download_downloading">Downloading</string>
    <string name="exo_download_failed">Download failed</string>
    <string name="exo_download_notification_channel_name">Downloads</string>
    <string name="exo_download_paused">Downloads paused</string>
    <string name="exo_download_paused_for_network">Downloads waiting for network</string>
    <string name="exo_download_paused_for_wifi">Downloads waiting for WiFi</string>
    <string name="exo_download_removing">Removing downloads</string>
    <string name="fallback_menu_item_copy_link">Copy link</string>
    <string name="fallback_menu_item_open_in_browser">Open in browser</string>
    <string name="fallback_menu_item_share_link">Share link</string>
    <string name="gcm_defaultSenderId" translatable="false">601890245278</string>
    <string name="google_api_key" translatable="false">AIzaSyA1gSOHfjXGaqxANRtX53tbXoAxje_KbBk</string>
    <string name="google_app_id" translatable="false">1:601890245278:android:bc1822b6fd63eb585c498f</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyA1gSOHfjXGaqxANRtX53tbXoAxje_KbBk</string>
    <string name="google_storage_bucket" translatable="false">learning-app-119d3.firebasestorage.app</string>
    <string name="project_id" translatable="false">learning-app-119d3</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="wallet_buy_button_place_holder">Buy with Google</string>
    <style name="CheckoutTheme">

        <item name="android:background">@android:color/transparent</item>
        
        <item name="android:windowNoTitle">true</item>
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:colorBackgroundCacheHint">@null</item>
        
        <item name="android:windowIsTranslucent">true</item>
        
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowContentOverlay">@null</item>
        

    </style>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="WalletFragmentDefaultButtonTextAppearance">
<item name="android:textColor">@color/wallet_primary_text_holo_light</item>
<item name="android:textColorHighlight">@color/wallet_highlighted_text_holo_light</item>
<item name="android:textColorHint">@color/wallet_hint_foreground_holo_light</item>
<item name="android:textColorLink">@color/wallet_link_text_light</item>
<item name="android:textSize">18sp</item>
<item name="android:textStyle">normal</item>
</style>
    <style name="WalletFragmentDefaultDetailsHeaderTextAppearance" parent="WalletFragmentDefaultDetailsTextAppearance">
<item name="android:textStyle">bold</item>
</style>
    <style name="WalletFragmentDefaultDetailsTextAppearance">
<item name="android:textColor">@color/wallet_secondary_text_holo_dark</item>
<item name="android:textColorHighlight">@color/wallet_highlighted_text_holo_dark</item>
<item name="android:textColorHint">@color/wallet_hint_foreground_holo_dark</item>
<item name="android:textColorLink">@color/wallet_holo_blue_light</item>
<item name="android:textSize">14sp</item>
<item name="android:textStyle">normal</item>
</style>
    <style name="WalletFragmentDefaultStyle">
<item name="buyButtonHeight">48dp</item>
<item name="buyButtonWidth">match_parent</item>
<item name="buyButtonText">buy_with</item>
<item name="buyButtonAppearance">android_pay_dark</item>
<item name="maskedWalletDetailsTextAppearance">@style/WalletFragmentDefaultDetailsTextAppearance</item>
<item name="maskedWalletDetailsHeaderTextAppearance">@style/WalletFragmentDefaultDetailsHeaderTextAppearance</item>
<item name="maskedWalletDetailsBackground">@color/wallet_bright_foreground_holo_light</item>
<item name="maskedWalletDetailsButtonBackground">@android:drawable/btn_default</item>
<item name="maskedWalletDetailsButtonTextAppearance">@style/WalletFragmentDefaultButtonTextAppearance</item>
<item name="maskedWalletDetailsLogoTextColor">@color/wallet_bright_foreground_holo_dark</item>
<item name="maskedWalletDetailsLogoImageType">android_pay</item>
</style>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <declare-styleable name="ActivityFilter">
        
        <attr format="string" name="activityName"/>
        
        <attr format="string" name="activityAction"/>
    </declare-styleable>
    <declare-styleable name="ActivityRule">
        
        <attr format="boolean" name="alwaysExpand"/>
        <attr name="tag"/>
    </declare-styleable>
    <declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable>
    <declare-styleable name="CustomWalletTheme"><attr name="windowTransitionStyle">
<enum name="slide" value="1"/>

<enum name="none" value="2"/>

</attr>
<attr name="toolbarTextColorStyle">
<enum name="light" value="1"/>

<enum name="dark" value="2"/>

</attr>
<attr name="customThemeStyle">
<enum name="material" value="1"/>

<enum name="googleMaterial2" value="2"/>

</attr>
</declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LoadingImageView"><attr name="imageAspectRatioAdjust">
<enum name="none" value="0"/>

<enum name="adjust_width" value="1"/>

<enum name="adjust_height" value="2"/>

</attr>

<attr format="float" name="imageAspectRatio"/>

<attr format="boolean" name="circleCrop"/>
</declare-styleable>
    <declare-styleable name="MapAttrs"><attr name="mapType">
<enum name="none" value="0"/>

<enum name="normal" value="1"/>

<enum name="satellite" value="2"/>

<enum name="terrain" value="3"/>

<enum name="hybrid" value="4"/>

</attr>

<attr format="float" name="cameraBearing"/>

<attr format="float" name="cameraTargetLat"/>

<attr format="float" name="cameraTargetLng"/>

<attr format="float" name="cameraTilt"/>

<attr format="float" name="cameraZoom"/>

<attr format="boolean" name="liteMode"/>

<attr format="boolean" name="uiCompass"/>

<attr format="boolean" name="uiRotateGestures"/>

<attr format="boolean" name="uiScrollGestures"/>

<attr format="boolean" name="uiScrollGesturesDuringRotateOrZoom"/>

<attr format="boolean" name="uiTiltGestures"/>

<attr format="boolean" name="uiZoomControls"/>

<attr format="boolean" name="uiZoomGestures"/>

<attr format="boolean" name="useViewLifecycle"/>

<attr format="boolean" name="zOrderOnTop"/>

<attr format="boolean" name="uiMapToolbar"/>

<attr format="boolean" name="ambientEnabled"/>

<attr format="float" name="cameraMinZoomPreference"/>

<attr format="float" name="cameraMaxZoomPreference"/>

<attr format="float" name="latLngBoundsSouthWestLatitude"/>

<attr format="float" name="latLngBoundsSouthWestLongitude"/>

<attr format="float" name="latLngBoundsNorthEastLatitude"/>

<attr format="float" name="latLngBoundsNorthEastLongitude"/>
</declare-styleable>
    <declare-styleable name="SignInButton"><attr format="reference" name="buttonSize">
<enum name="standard" value="0"/>

<enum name="wide" value="1"/>

<enum name="icon_only" value="2"/>

</attr>
<attr format="reference" name="colorScheme">
<enum name="dark" value="0"/>

<enum name="light" value="1"/>

<enum name="auto" value="2"/>

</attr>

<attr format="reference|string" name="scopeUris"/>
</declare-styleable>
    <declare-styleable name="SplitPairFilter">
        
        <attr format="string" name="primaryActivityName"/>
        
        <attr format="string" name="secondaryActivityName"/>
        
        <attr format="string" name="secondaryActivityAction"/>
    </declare-styleable>
    <declare-styleable name="SplitPairRule">
        
        <attr format="boolean" name="clearTop"/>
        <attr name="finishPrimaryWithSecondary"/>
        <attr name="finishSecondaryWithPrimary"/>
        <attr name="splitRatio"/>
        <attr name="splitMinWidthDp"/>
        <attr name="splitMinHeightDp"/>
        <attr name="splitMinSmallestWidthDp"/>
        <attr name="splitMaxAspectRatioInPortrait"/>
        <attr name="splitMaxAspectRatioInLandscape"/>
        <attr name="splitLayoutDirection"/>
        <attr name="tag"/>
        <attr name="animationBackgroundColor"/>
    </declare-styleable>
    <declare-styleable name="SplitPlaceholderRule">
        
        <attr format="string" name="placeholderActivityName"/>
        
        <attr format="boolean" name="stickyPlaceholder"/>
        <attr name="finishPrimaryWithPlaceholder"/>
        <attr name="splitRatio"/>
        <attr name="splitMinWidthDp"/>
        <attr name="splitMinHeightDp"/>
        <attr name="splitMinSmallestWidthDp"/>
        <attr name="splitMaxAspectRatioInPortrait"/>
        <attr name="splitMaxAspectRatioInLandscape"/>
        <attr name="splitLayoutDirection"/>
        <attr name="tag"/>
        <attr name="animationBackgroundColor"/>
    </declare-styleable>
    <declare-styleable name="WalletFragmentOptions"><attr name="appTheme">
<enum name="holo_dark" value="0"/>

<enum name="holo_light" value="1"/>

</attr>
<attr name="environment">
<enum name="production" value="1"/>

<enum name="test" value="3"/>

<enum name="sandbox" value="0"/>

<enum name="strict_sandbox" value="2"/>

</attr>

<attr format="reference" name="fragmentStyle"/>
<attr name="fragmentMode">
<enum name="buyButton" value="1"/>

<enum name="selectionDetails" value="2"/>

</attr>
</declare-styleable>
    <declare-styleable name="WalletFragmentStyle"><attr format="dimension" name="buyButtonHeight">
<enum name="match_parent" value="-1"/>

<enum name="wrap_content" value="-2"/>

</attr>
<attr format="dimension" name="buyButtonWidth">
<enum name="match_parent" value="-1"/>

<enum name="wrap_content" value="-2"/>

</attr>
<attr name="buyButtonText">
<enum name="buy_with" value="5"/>

<enum name="logo_only" value="6"/>

<enum name="donate_with" value="7"/>

<enum name="buy_with_google" value="1"/>

<enum name="buy_now" value="2"/>

<enum name="book_now" value="3"/>

<enum name="donate_with_google" value="4"/>

</attr>
<attr name="buyButtonAppearance">
<enum name="google_wallet_classic" value="1"/>

<enum name="google_wallet_grayscale" value="2"/>

<enum name="google_wallet_monochrome" value="3"/>

<enum name="android_pay_dark" value="4"/>

<enum name="android_pay_light" value="5"/>

<enum name="android_pay_light_with_border" value="6"/>

<enum name="classic" value="1"/>

<enum name="grayscale" value="2"/>

<enum name="monochrome" value="3"/>

</attr>

<attr format="reference" name="maskedWalletDetailsTextAppearance"/>

<attr format="reference" name="maskedWalletDetailsHeaderTextAppearance"/>

<attr format="color|reference" name="maskedWalletDetailsBackground"/>

<attr format="reference" name="maskedWalletDetailsButtonTextAppearance"/>

<attr format="color|reference" name="maskedWalletDetailsButtonBackground"/>

<attr format="color" name="maskedWalletDetailsLogoTextColor"/>
<attr name="maskedWalletDetailsLogoImageType">
<enum name="google_wallet_classic" value="1"/>

<enum name="google_wallet_monochrome" value="2"/>

<enum name="android_pay" value="3"/>

<enum name="classic" value="1"/>

<enum name="monochrome" value="2"/>

</attr>
</declare-styleable>
</resources>