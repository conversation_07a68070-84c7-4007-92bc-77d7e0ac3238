<?xml version="1.0" encoding="utf-8"?>
<resources>
    <bool name="isTablet">true</bool>
    <style name="CheckoutTheme" parent="android:style/Theme.Dialog">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>