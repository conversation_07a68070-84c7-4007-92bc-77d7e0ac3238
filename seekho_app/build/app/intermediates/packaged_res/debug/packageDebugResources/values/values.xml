<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="default_web_client_id" translatable="false">601890245278-jbg05oftf76pprvmu52jq1gdjmmjfnbl.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">601890245278</string>
    <string name="google_api_key" translatable="false">AIzaSyA1gSOHfjXGaqxANRtX53tbXoAxje_KbBk</string>
    <string name="google_app_id" translatable="false">1:601890245278:android:bc1822b6fd63eb585c498f</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyA1gSOHfjXGaqxANRtX53tbXoAxje_KbBk</string>
    <string name="google_storage_bucket" translatable="false">learning-app-119d3.firebasestorage.app</string>
    <string name="project_id" translatable="false">learning-app-119d3</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>