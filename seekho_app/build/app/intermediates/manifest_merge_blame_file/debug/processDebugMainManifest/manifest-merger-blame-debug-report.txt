1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gumbo.learning"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:6:5-66
15-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/debug/AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:39:5-44:15
24        <intent>
24-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:41:13-72
25-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
27-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:11:9-17:18
30            <action android:name="android.intent.action.VIEW" />
30-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
30-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
31
32            <data
32-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
33                android:mimeType="*/*"
33-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:19-48
34                android:scheme="*" />
35        </intent>
36        <intent>
36-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:18:9-27:18
37            <action android:name="android.intent.action.VIEW" />
37-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
37-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
38
39            <category android:name="android.intent.category.BROWSABLE" />
39-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
39-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
40
41            <data
41-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
42                android:host="pay"
43                android:mimeType="*/*"
43-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:19-48
44                android:scheme="upi" />
45        </intent>
46        <intent>
46-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:28:9-30:18
47            <action android:name="android.intent.action.MAIN" />
47-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:13-65
47-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:21-62
48        </intent>
49        <intent>
49-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:31:9-35:18
50            <action android:name="android.intent.action.SEND" />
50-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:32:13-65
50-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:32:21-62
51
52            <data android:mimeType="*/*" />
52-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
52-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:19-48
53        </intent>
54        <intent>
54-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:36:9-38:18
55            <action android:name="rzp.device_token.share" />
55-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:37:13-61
55-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:37:21-58
56        </intent>
57    </queries>
58
59    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
59-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:5-79
59-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:26:22-76
60    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
60-->[com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:5-98
60-->[com.google.android.recaptcha:recaptcha:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/4e260382871c528da7b61d84dd06ad26/transformed/jetified-recaptcha-18.4.0/AndroidManifest.xml:9:22-95
61
62    <uses-feature
62-->[com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:26:5-28:35
63        android:glEsVersion="0x00020000"
63-->[com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:27:9-41
64        android:required="true" />
64-->[com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:28:9-32
65
66    <permission
66-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
67        android:name="com.gumbo.learning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
67-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
68        android:protectionLevel="signature" />
68-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
69
70    <uses-permission android:name="com.gumbo.learning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
70-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
70-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
71
72    <application
73        android:name="android.app.Application"
74        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
74-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/dfc2fbca530748e569b0737b09fa016f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
75        android:debuggable="true"
76        android:extractNativeLibs="true"
77        android:icon="@mipmap/ic_launcher"
78        android:label="Gumbo Learning" >
79        <activity
80            android:name="com.gumbo.learning.MainActivity"
81            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
82            android:exported="true"
83            android:hardwareAccelerated="true"
84            android:launchMode="singleTop"
85            android:taskAffinity=""
86            android:theme="@style/LaunchTheme"
87            android:windowSoftInputMode="adjustResize" >
88
89            <!--
90                 Specifies an Android theme to apply to this Activity as soon as
91                 the Android process has started. This theme is visible to the user
92                 while the Flutter UI initializes. After that, this theme continues
93                 to determine the Window background behind the Flutter UI.
94            -->
95            <meta-data
96                android:name="io.flutter.embedding.android.NormalTheme"
97                android:resource="@style/NormalTheme" />
98
99            <intent-filter>
100                <action android:name="android.intent.action.MAIN" />
100-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:13-65
100-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:21-62
101
102                <category android:name="android.intent.category.LAUNCHER" />
103            </intent-filter>
104        </activity>
105        <!--
106             Don't delete the meta-data below.
107             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
108        -->
109        <meta-data
110            android:name="flutterEmbedding"
111            android:value="2" />
112
113        <service
113-->[:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
114            android:name="com.google.firebase.components.ComponentDiscoveryService"
114-->[:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:18-89
115            android:directBootAware="true"
115-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:32:13-43
116            android:exported="false" >
116-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:68:13-37
117            <meta-data
117-->[:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
118                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
118-->[:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[:firebase_auth] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_auth/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
120            <meta-data
120-->[:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
121                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
121-->[:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[:firebase_core] /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
123            <meta-data
123-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:69:13-71:85
124                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
124-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:70:17-109
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:71:17-82
126            <meta-data
126-->[com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:12:13-14:85
127                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
127-->[com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:13:17-116
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-common-ktx:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/7872a1178712ba0f6ecf207676e97ecb/transformed/jetified-firebase-common-ktx-20.4.3/AndroidManifest.xml:14:17-82
129            <meta-data
129-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:35:13-37:85
130                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
130-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:36:17-109
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:37:17-82
132        </service>
133
134        <activity
134-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:29:9-46:20
135            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
135-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:30:13-80
136            android:excludeFromRecents="true"
136-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:31:13-46
137            android:exported="true"
137-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:32:13-36
138            android:launchMode="singleTask"
138-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:33:13-44
139            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
139-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:34:13-72
140            <intent-filter>
140-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:35:13-45:29
141                <action android:name="android.intent.action.VIEW" />
141-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
141-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
142
143                <category android:name="android.intent.category.DEFAULT" />
143-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:17-76
143-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:27-73
144                <category android:name="android.intent.category.BROWSABLE" />
144-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
144-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
145
146                <data
146-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
147                    android:host="firebase.auth"
148                    android:path="/"
149                    android:scheme="genericidp" />
150            </intent-filter>
151        </activity>
152        <activity
152-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:47:9-64:20
153            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
153-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:48:13-79
154            android:excludeFromRecents="true"
154-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:49:13-46
155            android:exported="true"
155-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:50:13-36
156            android:launchMode="singleTask"
156-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:51:13-44
157            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
157-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:52:13-72
158            <intent-filter>
158-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:53:13-63:29
159                <action android:name="android.intent.action.VIEW" />
159-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:17-69
159-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:36:25-66
160
161                <category android:name="android.intent.category.DEFAULT" />
161-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:17-76
161-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:38:27-73
162                <category android:name="android.intent.category.BROWSABLE" />
162-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:17-78
162-->[com.google.firebase:firebase-auth:22.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/1aebbadb8cfa120dfba1b908806ef748/transformed/jetified-firebase-auth-22.3.1/AndroidManifest.xml:39:27-75
163
164                <data
164-->/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/android/app/src/main/AndroidManifest.xml:42:13-50
165                    android:host="firebase.auth"
166                    android:path="/"
167                    android:scheme="recaptcha" />
168            </intent-filter>
169        </activity>
170        <activity
170-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:42:9-50:20
171            android:name="com.razorpay.CheckoutActivity"
171-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:43:13-57
172            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
172-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:44:13-83
173            android:exported="false"
173-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:45:13-37
174            android:theme="@style/CheckoutTheme" >
174-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:46:13-49
175            <intent-filter>
175-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:47:13-49:29
176                <action android:name="android.intent.action.MAIN" />
176-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:13-65
176-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:29:21-62
177            </intent-filter>
178        </activity>
179
180        <provider
180-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:52:9-60:20
181            android:name="androidx.startup.InitializationProvider"
181-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:53:13-67
182            android:authorities="com.gumbo.learning.androidx-startup"
182-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:54:13-68
183            android:exported="false" >
183-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:55:13-37
184            <meta-data
184-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:57:13-59:52
185                android:name="com.razorpay.RazorpayInitializer"
185-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:58:17-64
186                android:value="androidx.startup" />
186-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:59:17-49
187            <meta-data
187-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
188                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
188-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
189                android:value="androidx.startup" />
189-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/bb10008c4b33ee9597583412a41828ed/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
190            <meta-data
190-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
191                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
191-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
192                android:value="androidx.startup" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
193        </provider>
194
195        <activity
195-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:62:9-65:75
196            android:name="com.razorpay.MagicXActivity"
196-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:63:13-55
197            android:exported="false"
197-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:64:13-37
198            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
198-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:65:13-72
199
200        <meta-data
200-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:67:9-69:58
201            android:name="com.razorpay.plugin.googlepay_all"
201-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:68:13-61
202            android:value="com.razorpay.RzpGpayMerged" />
202-->[com.razorpay:standard-core:1.6.50] /Users/<USER>/.gradle/caches/8.10.2/transforms/03ab26b31c121233a5db1198e1853244/transformed/jetified-standard-core-1.6.50/AndroidManifest.xml:69:13-55
203
204        <activity
204-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:23:9-27:75
205            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
205-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:24:13-93
206            android:excludeFromRecents="true"
206-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:25:13-46
207            android:exported="false"
207-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:26:13-37
208            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
208-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:27:13-72
209        <!--
210            Service handling Google Sign-In user revocation. For apps that do not integrate with
211            Google Sign-In, this service will never be started.
212        -->
213        <service
213-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:33:9-37:51
214            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
214-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:34:13-89
215            android:exported="true"
215-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:35:13-36
216            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
216-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:36:13-107
217            android:visibleToInstantApps="true" />
217-->[com.google.android.gms:play-services-auth:21.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0c1904752f657b93a7da3682d313879/transformed/jetified-play-services-auth-21.1.0/AndroidManifest.xml:37:13-48
218
219        <provider
219-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:23:9-28:39
220            android:name="com.google.firebase.provider.FirebaseInitProvider"
220-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:24:13-77
221            android:authorities="com.gumbo.learning.firebaseinitprovider"
221-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:25:13-72
222            android:directBootAware="true"
222-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:26:13-43
223            android:exported="false"
223-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:27:13-37
224            android:initOrder="100" />
224-->[com.google.firebase:firebase-common:20.4.3] /Users/<USER>/.gradle/caches/8.10.2/transforms/67fdfc7e59ca55c3dc07e9e88ccc4671/transformed/jetified-firebase-common-20.4.3/AndroidManifest.xml:28:13-36
225
226        <uses-library
226-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
227            android:name="androidx.window.extensions"
227-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
228            android:required="false" />
228-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
229        <uses-library
229-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
230            android:name="androidx.window.sidecar"
230-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
231            android:required="false" /> <!-- Needs to be explicitly declared on P+ -->
231-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aed2861b2b6894f7067df3f3f96b791f/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
232        <uses-library
232-->[com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:33:9-35:40
233            android:name="org.apache.http.legacy"
233-->[com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:34:13-50
234            android:required="false" />
234-->[com.google.android.gms:play-services-maps:17.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/310518277de1c045777cf755ee855336/transformed/jetified-play-services-maps-17.0.0/AndroidManifest.xml:35:13-37
235
236        <activity
236-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:9-22:45
237            android:name="com.google.android.gms.common.api.GoogleApiActivity"
237-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:20:19-85
238            android:exported="false"
238-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:22:19-43
239            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
239-->[com.google.android.gms:play-services-base:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/b93785a04fcd49acabbaa600426866db/transformed/jetified-play-services-base-18.3.0/AndroidManifest.xml:21:19-78
240
241        <meta-data
241-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
242            android:name="com.google.android.gms.version"
242-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
243            android:value="@integer/google_play_services_version" />
243-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/48bb13e58e49cdf1e6377472107b28a8/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
244
245        <receiver
245-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
246            android:name="androidx.profileinstaller.ProfileInstallReceiver"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
247            android:directBootAware="false"
247-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
248            android:enabled="true"
248-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
249            android:exported="true"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
250            android:permission="android.permission.DUMP" >
250-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
252                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
252-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
255                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
255-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
258                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
258-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
261                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
261-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5aacd87baa6feaa7bece87b01e7698f0/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
262            </intent-filter>
263        </receiver>
264    </application>
265
266</manifest>
