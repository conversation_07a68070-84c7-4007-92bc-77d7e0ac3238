import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_constants.dart';

class FeaturesList extends StatelessWidget {
  final List<String> features;

  const FeaturesList({
    super.key,
    required this.features,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: AppColors.tertiaryText.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: features.map((feature) => Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: AppColors.successColor,
                size: 16,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Text(
                  feature,
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ],
          ),
        )).toList(),
      ),
    );
  }
}
