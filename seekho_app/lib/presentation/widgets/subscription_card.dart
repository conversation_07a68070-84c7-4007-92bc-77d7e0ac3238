import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/subscription.dart';

class SubscriptionCard extends StatelessWidget {
  final SubscriptionPlan plan;
  final bool isRecommended;
  final VoidCallback onSubscribe;
  final bool isLoading;

  const SubscriptionCard({
    super.key,
    required this.plan,
    required this.isRecommended,
    required this.onSubscribe,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: isRecommended ? AppColors.primaryGradient : null,
        color: isRecommended ? null : AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: isRecommended 
              ? Colors.transparent 
              : AppColors.tertiaryText.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Plan Name
                Text(
                  plan.name,
                  style: AppTextStyles.h5.copyWith(
                    color: isRecommended ? Colors.white : AppColors.primaryText,
                  ),
                ),
                
                const SizedBox(height: AppConstants.smallPadding),
                
                // Price
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      plan.formattedPrice,
                      style: AppTextStyles.priceText.copyWith(
                        color: isRecommended ? Colors.white : AppColors.premiumGold,
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      plan.isTrial ? 'for ${plan.durationDays} days' : '/month',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: isRecommended 
                            ? Colors.white.withOpacity(0.8) 
                            : AppColors.secondaryText,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Description
                Text(
                  plan.description,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isRecommended 
                        ? Colors.white.withOpacity(0.9) 
                        : AppColors.secondaryText,
                  ),
                ),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Subscribe Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: isLoading ? null : onSubscribe,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isRecommended 
                          ? Colors.white 
                          : AppColors.primaryAccent,
                      foregroundColor: isRecommended 
                          ? AppColors.primaryAccent 
                          : Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      ),
                      elevation: 0,
                    ),
                    child: isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                isRecommended 
                                    ? AppColors.primaryAccent 
                                    : Colors.white,
                              ),
                            ),
                          )
                        : Text(
                            plan.isTrial ? 'Start Trial' : 'Subscribe',
                            style: AppTextStyles.buttonLarge.copyWith(
                              color: isRecommended 
                                  ? AppColors.primaryAccent 
                                  : Colors.white,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
          
          // Recommended Badge
          if (isRecommended)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: const BoxDecoration(
                  color: AppColors.premiumGold,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(AppConstants.borderRadius),
                    bottomLeft: Radius.circular(AppConstants.borderRadius),
                  ),
                ),
                child: Text(
                  'RECOMMENDED',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
