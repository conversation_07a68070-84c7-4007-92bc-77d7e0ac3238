import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/category.dart';

class CategoryGrid extends StatelessWidget {
  final List<Category> categories;

  const CategoryGrid({
    super.key,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    // Mock categories for demo
    final mockCategories = [
      {'name': '<PERSON><PERSON><PERSON>', 'color': AppColors.sarkariKaamColor, 'icon': Icons.work},
      {'name': 'Part Time', 'color': AppColors.partTimeColor, 'icon': Icons.schedule},
      {'name': 'Gaming', 'color': AppColors.gamingColor, 'icon': Icons.games},
      {'name': 'Instagram', 'color': AppColors.instagramColor, 'icon': Icons.camera_alt},
      {'name': 'YouTube', 'color': AppColors.youtubeColor, 'icon': Icons.play_circle},
      {'name': 'English', 'color': AppColors.englishColor, 'icon': Icons.language},
      {'name': 'Astrology', 'color': AppColors.astrologyColor, 'icon': Icons.star},
      {'name': 'View All', 'color': AppColors.viewAllColor, 'icon': Icons.grid_view},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
        childAspectRatio: 0.8,
      ),
      itemCount: mockCategories.length,
      itemBuilder: (context, index) {
        final category = mockCategories[index];
        return CategoryTile(
          name: category['name'] as String,
          color: category['color'] as Color,
          icon: category['icon'] as IconData,
          onTap: () => _onCategoryTap(context, category['name'] as String),
        );
      },
    );
  }

  void _onCategoryTap(BuildContext context, String categoryName) {
    // TODO: Navigate to category screen
    // Navigator.pushNamed(context, '/category', arguments: categoryName);
  }
}

class CategoryTile extends StatelessWidget {
  final String name;
  final Color color;
  final IconData icon;
  final VoidCallback onTap;

  const CategoryTile({
    super.key,
    required this.name,
    required this.color,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            name,
            style: AppTextStyles.categoryTitle,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

class CategoryGridShimmer extends StatelessWidget {
  const CategoryGridShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: AppConstants.defaultPadding,
        mainAxisSpacing: AppConstants.defaultPadding,
        childAspectRatio: 0.8,
      ),
      itemCount: 8,
      itemBuilder: (context, index) {
        return const CategoryTileShimmer();
      },
    );
  }
}

class CategoryTileShimmer extends StatelessWidget {
  const CategoryTileShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.shimmerBase,
      highlightColor: AppColors.shimmerHighlight,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.shimmerBase,
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Container(
            width: 50,
            height: 12,
            decoration: BoxDecoration(
              color: AppColors.shimmerBase,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ],
      ),
    );
  }
}
