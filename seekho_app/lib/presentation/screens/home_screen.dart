import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../providers/content_provider.dart';
import '../widgets/category_grid.dart';
import '../widgets/video_carousel.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/app_bar_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _loadData() {
    final contentProvider = context.read<ContentProvider>();
    contentProvider.loadCategories();
    contentProvider.loadTopVideos();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBackground,
      body: SafeArea(
        child: Column(
          children: [
            // App Bar
            const AppBarWidget(),
            
            // Content
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  _loadData();
                },
                backgroundColor: AppColors.cardBackground,
                color: AppColors.primaryAccent,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Search Bar
                      const SearchBarWidget(),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Categories Section
                      Consumer<ContentProvider>(
                        builder: (context, contentProvider, child) {
                          if (contentProvider.isLoadingCategories) {
                            return const CategoryGridShimmer();
                          }
                          
                          if (contentProvider.categoriesState == ContentState.error) {
                            return _buildErrorWidget(
                              contentProvider.errorMessage ?? 'Failed to load categories',
                              () => contentProvider.loadCategories(),
                            );
                          }
                          
                          return CategoryGrid(categories: contentProvider.categories);
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Top Videos Section
                      Row(
                        children: [
                          Icon(
                            Icons.trending_up,
                            color: AppColors.primaryAccent,
                            size: 20,
                          ),
                          const SizedBox(width: AppConstants.smallPadding),
                          Text(
                            'Top Videos',
                            style: AppTextStyles.sectionTitle,
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      Consumer<ContentProvider>(
                        builder: (context, contentProvider, child) {
                          if (contentProvider.isLoadingTopVideos) {
                            return const VideoCarouselShimmer();
                          }
                          
                          if (contentProvider.topVideosState == ContentState.error) {
                            return _buildErrorWidget(
                              contentProvider.errorMessage ?? 'Failed to load videos',
                              () => contentProvider.loadTopVideos(),
                            );
                          }
                          
                          return VideoCarousel(
                            videos: contentProvider.topVideos,
                            onVideoTap: (video) {
                              // Navigate to video player
                              _navigateToVideoPlayer(video.id);
                            },
                          );
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.largePadding),
                      
                      // Additional sections can be added here
                      // Part Time Income, Gaming, Instagram sections
                      
                      const SizedBox(height: 100), // Bottom padding for navigation bar
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message, VoidCallback onRetry) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: AppColors.errorColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.errorColor,
            size: 32,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.errorColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryAccent,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
            ),
            child: Text(
              'Retry',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToVideoPlayer(String videoId) {
    // TODO: Implement navigation to video player
    // Navigator.pushNamed(context, '/video-player', arguments: videoId);
  }
}
