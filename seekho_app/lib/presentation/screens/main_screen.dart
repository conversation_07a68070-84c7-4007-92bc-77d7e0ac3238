import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import 'home_screen.dart';
import 'plus_screen.dart';
import 'new_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const PlusScreen(),
    const NewScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBackground,
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          color: AppColors.bottomNavBackground,
          border: Border(
            top: BorderSide(
              color: AppColors.cardBackground,
              width: 0.5,
            ),
          ),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          backgroundColor: Colors.transparent,
          elevation: 0,
          type: BottomNavigationBarType.fixed,
          selectedItemColor: AppColors.bottomNavSelected,
          unselectedItemColor: AppColors.bottomNavUnselected,
          selectedLabelStyle: AppTextStyles.tabText.copyWith(
            color: AppColors.bottomNavSelected,
          ),
          unselectedLabelStyle: AppTextStyles.tabText.copyWith(
            color: AppColors.bottomNavUnselected,
          ),
          items: [
            BottomNavigationBarItem(
              icon: Icon(
                _currentIndex == 0 ? Icons.home : Icons.home_outlined,
                size: 24,
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  gradient: _currentIndex == 1 
                      ? AppColors.primaryGradient 
                      : null,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _currentIndex == 1 ? Icons.add : Icons.add_outlined,
                  size: 20,
                  color: _currentIndex == 1 
                      ? Colors.white 
                      : AppColors.bottomNavUnselected,
                ),
              ),
              label: 'Plus',
            ),
            BottomNavigationBarItem(
              icon: Stack(
                children: [
                  Icon(
                    _currentIndex == 2 ? Icons.fiber_new : Icons.fiber_new_outlined,
                    size: 24,
                  ),
                  if (_currentIndex != 2)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: AppColors.errorColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
              label: 'New',
            ),
          ],
        ),
      ),
    );
  }
}
