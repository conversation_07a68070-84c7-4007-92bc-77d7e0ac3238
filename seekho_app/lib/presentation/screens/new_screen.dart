import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../providers/content_provider.dart';
import '../widgets/video_list_item.dart';

class NewScreen extends StatefulWidget {
  const NewScreen({super.key});

  @override
  State<NewScreen> createState() => _NewScreenState();
}

class _NewScreenState extends State<NewScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _loadData() {
    final contentProvider = context.read<ContentProvider>();
    contentProvider.loadRecentVideos();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBackground,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: Consumer<ContentProvider>(
                builder: (context, contentProvider, child) {
                  if (contentProvider.isLoadingRecentVideos) {
                    return _buildLoadingView();
                  }
                  
                  if (contentProvider.recentVideosState == ContentState.error) {
                    return _buildErrorView(
                      contentProvider.errorMessage ?? 'Failed to load recent videos',
                      () => contentProvider.loadRecentVideos(),
                    );
                  }
                  
                  return _buildRecentVideosView(contentProvider);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        border: Border(
          bottom: BorderSide(
            color: AppColors.tertiaryText.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.fiber_new,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'What\'s New',
                  style: AppTextStyles.h4,
                ),
                Text(
                  'Recently released videos',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Implement filter/sort options
            },
            icon: Icon(
              Icons.filter_list,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: 6,
      itemBuilder: (context, index) {
        return const VideoListItemShimmer();
      },
    );
  }

  Widget _buildErrorView(String message, VoidCallback onRetry) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppColors.errorColor,
              size: 48,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              message,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.errorColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryAccent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentVideosView(ContentProvider contentProvider) {
    final recentVideos = contentProvider.recentVideos;
    
    if (recentVideos.isEmpty) {
      return _buildEmptyView();
    }

    // Group videos by date
    final groupedVideos = _groupVideosByDate(recentVideos);

    return RefreshIndicator(
      onRefresh: () async {
        _loadData();
      },
      backgroundColor: AppColors.cardBackground,
      color: AppColors.primaryAccent,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: groupedVideos.length,
        itemBuilder: (context, index) {
          final entry = groupedVideos.entries.elementAt(index);
          final dateLabel = entry.key;
          final videos = entry.value;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date Header
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.defaultPadding,
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primaryAccent.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppColors.primaryAccent.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        dateLabel,
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.primaryAccent,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Container(
                        height: 1,
                        color: AppColors.tertiaryText.withValues(alpha: 0.1),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Videos List
              ...videos.map((video) => Padding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.defaultPadding,
                ),
                child: VideoListItem(
                  video: video,
                  onTap: () => _navigateToVideoPlayer(video.id),
                  onMarkViewed: () => _markVideoAsViewed(video.id),
                ),
              )),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.video_library_outlined,
              color: AppColors.tertiaryText,
              size: 64,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No New Videos',
              style: AppTextStyles.h6.copyWith(
                color: AppColors.secondaryText,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Check back later for new content',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.tertiaryText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Map<String, List<dynamic>> _groupVideosByDate(List<dynamic> videos) {
    final Map<String, List<dynamic>> grouped = {};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    for (final video in videos) {
      final videoDate = DateTime(
        video.createdAt.year,
        video.createdAt.month,
        video.createdAt.day,
      );

      String dateLabel;
      if (videoDate == today) {
        dateLabel = 'TODAY';
      } else if (videoDate == yesterday) {
        dateLabel = 'YESTERDAY';
      } else {
        final daysAgo = today.difference(videoDate).inDays;
        if (daysAgo < 7) {
          dateLabel = '$daysAgo DAYS AGO';
        } else {
          dateLabel = '${videoDate.day}/${videoDate.month}/${videoDate.year}';
        }
      }

      if (!grouped.containsKey(dateLabel)) {
        grouped[dateLabel] = [];
      }
      grouped[dateLabel]!.add(video);
    }

    return grouped;
  }

  void _navigateToVideoPlayer(String videoId) {
    // TODO: Implement navigation to video player
    // Navigator.pushNamed(context, '/video-player', arguments: videoId);
  }

  void _markVideoAsViewed(String videoId) {
    final contentProvider = context.read<ContentProvider>();
    contentProvider.markVideoAsViewed(videoId);
  }
}
