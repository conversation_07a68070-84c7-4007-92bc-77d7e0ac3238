import 'package:flutter/foundation.dart';
import '../../domain/entities/category.dart' as domain;
import '../../domain/entities/video.dart';
import '../../domain/usecases/content/get_categories_usecase.dart';
import '../../domain/usecases/content/get_topics_usecase.dart';
import '../../domain/usecases/content/get_videos_usecase.dart';
import '../../domain/usecases/content/get_video_detail_usecase.dart';
import '../../domain/usecases/content/mark_video_viewed_usecase.dart';
import '../../core/error/failures.dart';

enum ContentState { initial, loading, loaded, error }

class ContentProvider extends ChangeNotifier {
  final GetCategoriesUseCase getCategoriesUseCase;
  final GetTopicsUseCase getTopicsUseCase;
  final GetVideosUseCase getVideosUseCase;
  final GetVideoDetailUseCase getVideoDetailUseCase;
  final MarkVideoViewedUseCase markVideoViewedUseCase;

  ContentProvider({
    required this.getCategoriesUseCase,
    required this.getTopicsUseCase,
    required this.getVideosUseCase,
    required this.getVideoDetailUseCase,
    required this.markVideoViewedUseCase,
  });

  ContentState _categoriesState = ContentState.initial;
  ContentState _topVideosState = ContentState.initial;
  ContentState _recentVideosState = ContentState.initial;

  List<domain.Category> _categories = [];
  List<Video> _topVideos = [];
  List<Video> _recentVideos = [];
  String? _errorMessage;

  // Getters
  ContentState get categoriesState => _categoriesState;
  ContentState get topVideosState => _topVideosState;
  ContentState get recentVideosState => _recentVideosState;

  List<domain.Category> get categories => _categories;
  List<Video> get topVideos => _topVideos;
  List<Video> get recentVideos => _recentVideos;
  String? get errorMessage => _errorMessage;

  bool get isLoadingCategories => _categoriesState == ContentState.loading;
  bool get isLoadingTopVideos => _topVideosState == ContentState.loading;
  bool get isLoadingRecentVideos => _recentVideosState == ContentState.loading;

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> loadCategories() async {
    _categoriesState = ContentState.loading;
    notifyListeners();

    final result = await getCategoriesUseCase.call();

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
        _categoriesState = ContentState.error;
      },
      (categories) {
        _categories = categories;
        _categoriesState = ContentState.loaded;
      },
    );
    notifyListeners();
  }

  Future<void> loadTopVideos() async {
    _topVideosState = ContentState.loading;
    notifyListeners();

    // For now, create mock data since the API endpoint might not be ready
    await Future.delayed(const Duration(milliseconds: 500));
    _topVideos = _createMockTopVideos();
    _topVideosState = ContentState.loaded;
    notifyListeners();
  }

  Future<void> loadRecentVideos() async {
    _recentVideosState = ContentState.loading;
    notifyListeners();

    // For now, create mock data since the API endpoint might not be ready
    await Future.delayed(const Duration(milliseconds: 500));
    _recentVideos = _createMockRecentVideos();
    _recentVideosState = ContentState.loaded;
    notifyListeners();
  }

  Future<void> markVideoAsViewed(String videoId) async {
    final result = await markVideoViewedUseCase.call(
      MarkVideoViewedParams(videoId: videoId),
    );

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
      },
      (_) {
        // Update the video in the lists
        _updateVideoViewStatus(videoId, true);
      },
    );
    notifyListeners();
  }

  void _updateVideoViewStatus(String videoId, bool isViewed) {
    // Update in top videos
    final topVideoIndex = _topVideos.indexWhere((video) => video.id == videoId);
    if (topVideoIndex != -1) {
      _topVideos[topVideoIndex] = _topVideos[topVideoIndex].copyWith(isViewed: isViewed);
    }

    // Update in recent videos
    final recentVideoIndex = _recentVideos.indexWhere((video) => video.id == videoId);
    if (recentVideoIndex != -1) {
      _recentVideos[recentVideoIndex] = _recentVideos[recentVideoIndex].copyWith(isViewed: isViewed);
    }
  }

  List<Video> _createMockTopVideos() {
    return [
      Video(
        id: '1',
        topicId: 'topic1',
        title: 'Master Future Perfect Tense',
        description: 'Learn the future perfect tense with examples',
        thumbnailUrl: 'https://example.com/thumb1.jpg',
        videoUrl: 'https://example.com/video1.mp4',
        duration: 600,
        order: 1,
        isActive: true,
        isPremium: false,
        isViewed: false,
        viewCount: 1250,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Video(
        id: '2',
        topicId: 'topic2',
        title: 'CE Option Strategy for PUT',
        description: 'Complete guide to CE option strategies',
        thumbnailUrl: 'https://example.com/thumb2.jpg',
        videoUrl: 'https://example.com/video2.mp4',
        duration: 900,
        order: 2,
        isActive: true,
        isPremium: true,
        isViewed: false,
        viewCount: 850,
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];
  }

  List<Video> _createMockRecentVideos() {
    return [
      Video(
        id: '3',
        topicId: 'topic3',
        title: 'Car Engine Kaise Kaam Karta Hai',
        description: 'Understanding car engine mechanics',
        thumbnailUrl: 'https://example.com/thumb3.jpg',
        videoUrl: 'https://example.com/video3.mp4',
        duration: 720,
        order: 1,
        isActive: true,
        isPremium: false,
        isViewed: false,
        viewCount: 2100,
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 3)),
      ),
    ];
  }

  String _mapFailureToMessage(Failure failure) {
    return switch (failure) {
      NetworkFailure() => 'Network error. Please check your internet connection.',
      ServerFailure() => 'Server error. Please try again later.',
      ContentNotFoundFailure() => 'Content not found.',
      _ => 'An unexpected error occurred. Please try again.',
    };
  }
}
