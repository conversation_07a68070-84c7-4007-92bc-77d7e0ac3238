import 'package:flutter/foundation.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/auth/google_sign_in_usecase.dart';
import '../../domain/usecases/auth/logout_usecase.dart';
import '../../domain/usecases/auth/refresh_token_usecase.dart';
import '../../domain/usecases/auth/get_user_usecase.dart';
import '../../core/error/failures.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  final GoogleSignInUseCase googleSignInUseCase;
  final LogoutUseCase logoutUseCase;
  final RefreshTokenUseCase refreshTokenUseCase;
  final GetUserUseCase getUserUseCase;

  AuthProvider({
    required this.googleSignInUseCase,
    required this.logoutUseCase,
    required this.refreshTokenUseCase,
    required this.getUserUseCase,
  });

  AuthState _state = AuthState.initial;
  User? _user;
  String? _errorMessage;

  AuthState get state => _state;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _state == AuthState.authenticated && _user != null;
  bool get isLoading => _state == AuthState.loading;

  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setState(AuthState.error);
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> signInWithGoogle() async {
    _setState(AuthState.loading);
    
    final result = await googleSignInUseCase.call();
    
    result.fold(
      (failure) => _setError(_mapFailureToMessage(failure)),
      (user) {
        _user = user;
        _setState(AuthState.authenticated);
      },
    );
  }

  Future<void> logout() async {
    _setState(AuthState.loading);
    
    final result = await logoutUseCase.call();
    
    result.fold(
      (failure) => _setError(_mapFailureToMessage(failure)),
      (_) {
        _user = null;
        _setState(AuthState.unauthenticated);
      },
    );
  }

  Future<void> getCurrentUser() async {
    _setState(AuthState.loading);
    
    final result = await getUserUseCase.call();
    
    result.fold(
      (failure) {
        _user = null;
        _setState(AuthState.unauthenticated);
      },
      (user) {
        _user = user;
        _setState(AuthState.authenticated);
      },
    );
  }

  Future<bool> refreshToken() async {
    final result = await refreshTokenUseCase.call();
    
    return result.fold(
      (failure) {
        _user = null;
        _setState(AuthState.unauthenticated);
        return false;
      },
      (token) {
        return true;
      },
    );
  }

  String _mapFailureToMessage(Failure failure) {
    return switch (failure) {
      AuthenticationFailure() => failure.message,
      NetworkFailure() => 'Network error. Please check your internet connection.',
      ServerFailure() => 'Server error. Please try again later.',
      _ => 'An unexpected error occurred. Please try again.',
    };
  }
}
