class ServerException implements Exception {
  final String message;
  final int? statusCode;
  
  const ServerException({
    required this.message,
    this.statusCode,
  });
  
  @override
  String toString() => 'ServerException: $message (Status: $statusCode)';
}

class NetworkException implements Exception {
  final String message;
  
  const NetworkException({required this.message});
  
  @override
  String toString() => 'NetworkException: $message';
}

class CacheException implements Exception {
  final String message;
  
  const CacheException({required this.message});
  
  @override
  String toString() => 'CacheException: $message';
}

class AuthenticationException implements Exception {
  final String message;
  final int? statusCode;
  
  const AuthenticationException({
    required this.message,
    this.statusCode,
  });
  
  @override
  String toString() => 'AuthenticationException: $message (Status: $statusCode)';
}

class AuthorizationException implements Exception {
  final String message;
  final int? statusCode;
  
  const AuthorizationException({
    required this.message,
    this.statusCode,
  });
  
  @override
  String toString() => 'AuthorizationException: $message (Status: $statusCode)';
}

class TokenExpiredException implements Exception {
  final String message;
  
  const TokenExpiredException({required this.message});
  
  @override
  String toString() => 'TokenExpiredException: $message';
}

class ValidationException implements Exception {
  final String message;
  final Map<String, List<String>>? errors;
  
  const ValidationException({
    required this.message,
    this.errors,
  });
  
  @override
  String toString() => 'ValidationException: $message';
}

class PaymentException implements Exception {
  final String message;
  final String? errorCode;
  
  const PaymentException({
    required this.message,
    this.errorCode,
  });
  
  @override
  String toString() => 'PaymentException: $message (Code: $errorCode)';
}

class SubscriptionException implements Exception {
  final String message;
  
  const SubscriptionException({required this.message});
  
  @override
  String toString() => 'SubscriptionException: $message';
}

class VideoException implements Exception {
  final String message;
  
  const VideoException({required this.message});
  
  @override
  String toString() => 'VideoException: $message';
}

class ContentNotFoundException implements Exception {
  final String message;
  
  const ContentNotFoundException({required this.message});
  
  @override
  String toString() => 'ContentNotFoundException: $message';
}

class ContentAccessDeniedException implements Exception {
  final String message;
  
  const ContentAccessDeniedException({required this.message});
  
  @override
  String toString() => 'ContentAccessDeniedException: $message';
}
