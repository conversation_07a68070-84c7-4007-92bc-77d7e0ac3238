import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? statusCode;
  
  const Failure({
    required this.message,
    this.statusCode,
  });
  
  @override
  List<Object?> get props => [message, statusCode];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.statusCode,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.statusCode,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.statusCode,
  });
}

// Authentication failures
class AuthenticationFailure extends Failure {
  const AuthenticationFailure({
    required super.message,
    super.statusCode,
  });
}

class AuthorizationFailure extends Failure {
  const AuthorizationFailure({
    required super.message,
    super.statusCode,
  });
}

class TokenExpiredFailure extends Failure {
  const TokenExpiredFailure({
    required super.message,
    super.statusCode,
  });
}

// Validation failures
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.statusCode,
  });
}

// Payment failures
class PaymentFailure extends Failure {
  const PaymentFailure({
    required super.message,
    super.statusCode,
  });
}

class SubscriptionFailure extends Failure {
  const SubscriptionFailure({
    required super.message,
    super.statusCode,
  });
}

// Video failures
class VideoLoadFailure extends Failure {
  const VideoLoadFailure({
    required super.message,
    super.statusCode,
  });
}

class VideoPlaybackFailure extends Failure {
  const VideoPlaybackFailure({
    required super.message,
    super.statusCode,
  });
}

// Content failures
class ContentNotFoundFailure extends Failure {
  const ContentNotFoundFailure({
    required super.message,
    super.statusCode,
  });
}

class ContentAccessDeniedFailure extends Failure {
  const ContentAccessDeniedFailure({
    required super.message,
    super.statusCode,
  });
}

// Unknown failure
class UnknownFailure extends Failure {
  const UnknownFailure({
    required super.message,
    super.statusCode,
  });
}
