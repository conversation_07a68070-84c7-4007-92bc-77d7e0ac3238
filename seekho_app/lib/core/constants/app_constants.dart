class AppConstants {
  // API Configuration
  static const String baseUrl = 'http://localhost:8000'; // Replace with actual API URL
  static const String apiVersion = '/api';

  // Authentication
  static const String googleClientId = 'YOUR_GOOGLE_CLIENT_ID'; // Replace with actual client ID

  // Connection timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  
  // Subscription Plans
  static const int trialPrice = 1; // ₹1
  static const int monthlyPrice = 199; // ₹199
  static const int trialDays = 7;
  
  // Razorpay Configuration
  static const String razorpayKeyId = 'YOUR_RAZORPAY_KEY_ID'; // Replace with actual key
  
  // App Configuration
  static const String appName = 'Gumbo Learning';
  static const String appVersion = '1.0.0';
  
  // Pagination
  static const int defaultPageSize = 20;
  
  // Video Configuration
  static const int maxVideoQuality = 720;
  static const bool autoPlayVideos = false;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
}
