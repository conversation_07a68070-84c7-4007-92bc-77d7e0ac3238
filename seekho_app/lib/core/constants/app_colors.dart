import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors (Dark Theme)
  static const Color primaryBackground = Color(0xFF121212);
  static const Color secondaryBackground = Color(0xFF1E1E1E);
  static const Color cardBackground = Color(0xFF2A2A2A);
  
  // Accent Colors (Vibrant)
  static const Color primaryAccent = Color(0xFF6C63FF); // Purple
  static const Color secondaryAccent = Color(0xFF00D4AA); // Teal
  static const Color tertiaryAccent = Color(0xFFFF6B6B); // Red
  static const Color quaternaryAccent = Color(0xFFFFD93D); // Yellow
  
  // Text Colors
  static const Color primaryText = Color(0xFFFFFFFF);
  static const Color secondaryText = Color(0xFFB3B3B3);
  static const Color tertiaryText = Color(0xFF666666);
  static const Color disabledText = Color(0xFF404040);
  
  // Category Colors
  static const Color sarkariKaamColor = Color(0xFFFF6B35); // Orange
  static const Color partTimeColor = Color(0xFF4ECDC4); // Teal
  static const Color gamingColor = Color(0xFFE74C3C); // Red
  static const Color instagramColor = Color(0xFFE91E63); // Pink
  static const Color youtubeColor = Color(0xFFFF0000); // YouTube Red
  static const Color englishColor = Color(0xFFFFA726); // Orange
  static const Color astrologyColor = Color(0xFF9C27B0); // Purple
  static const Color viewAllColor = Color(0xFF607D8B); // Blue Grey
  
  // Status Colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color infoColor = Color(0xFF2196F3);
  
  // Subscription Colors
  static const Color premiumGold = Color(0xFFFFD700);
  static const Color trialColor = Color(0xFF00BCD4);
  
  // Video Status Colors
  static const Color lockedColor = Color(0xFF757575);
  static const Color unlockedColor = Color(0xFF4CAF50);
  
  // Bottom Navigation
  static const Color bottomNavBackground = Color(0xFF1A1A1A);
  static const Color bottomNavSelected = Color(0xFF6C63FF);
  static const Color bottomNavUnselected = Color(0xFF666666);
  
  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF6C63FF), Color(0xFF3F51B5)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient premiumGradient = LinearGradient(
    colors: [Color(0xFFFFD700), Color(0xFFFFA000)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [Color(0xFF2A2A2A), Color(0xFF1E1E1E)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Shimmer Colors
  static const Color shimmerBase = Color(0xFF2A2A2A);
  static const Color shimmerHighlight = Color(0xFF3A3A3A);
  
  // Overlay Colors
  static const Color overlayColor = Color(0x80000000);
  static const Color blurOverlay = Color(0x60000000);
}
