import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:logger/logger.dart';

import '../network/api_client.dart';
import '../../data/datasources/auth_remote_data_source.dart';
import '../../data/datasources/content_remote_data_source.dart';
import '../../data/datasources/subscription_remote_data_source.dart';
import '../../data/datasources/auth_local_data_source.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../data/repositories/content_repository_impl.dart';
import '../../data/repositories/subscription_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/repositories/content_repository.dart';
import '../../domain/repositories/subscription_repository.dart';
import '../../domain/usecases/auth/google_sign_in_usecase.dart';
import '../../domain/usecases/auth/logout_usecase.dart';
import '../../domain/usecases/auth/refresh_token_usecase.dart';
import '../../domain/usecases/auth/get_user_usecase.dart';
import '../../domain/usecases/content/get_categories_usecase.dart';
import '../../domain/usecases/content/get_topics_usecase.dart';
import '../../domain/usecases/content/get_videos_usecase.dart';
import '../../domain/usecases/content/get_video_detail_usecase.dart';
import '../../domain/usecases/content/mark_video_viewed_usecase.dart';
import '../../domain/usecases/subscription/get_subscription_plans_usecase.dart';
import '../../domain/usecases/subscription/create_order_usecase.dart';
import '../../domain/usecases/subscription/verify_payment_usecase.dart';
import '../../domain/usecases/subscription/get_subscription_status_usecase.dart';
import '../../domain/usecases/subscription/cancel_subscription_usecase.dart';
import '../../presentation/providers/auth_provider.dart';
import '../../presentation/providers/content_provider.dart';
import '../../presentation/providers/subscription_provider.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // Core
  sl.registerLazySingleton<Logger>(() => Logger());
  sl.registerLazySingleton<FlutterSecureStorage>(
    () => const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
    ),
  );
  sl.registerLazySingleton<GoogleSignIn>(
    () => GoogleSignIn(
      scopes: ['email', 'profile'],
    ),
  );
  sl.registerLazySingleton<ApiClient>(
    () => ApiClient(
      secureStorage: sl(),
      logger: sl(),
    ),
  );

  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(apiClient: sl()),
  );
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(secureStorage: sl()),
  );
  sl.registerLazySingleton<ContentRemoteDataSource>(
    () => ContentRemoteDataSourceImpl(apiClient: sl()),
  );
  sl.registerLazySingleton<SubscriptionRemoteDataSource>(
    () => SubscriptionRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
      googleSignIn: sl(),
    ),
  );
  sl.registerLazySingleton<ContentRepository>(
    () => ContentRepositoryImpl(remoteDataSource: sl()),
  );
  sl.registerLazySingleton<SubscriptionRepository>(
    () => SubscriptionRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases - Auth
  sl.registerLazySingleton(() => GoogleSignInUseCase(sl()));
  sl.registerLazySingleton(() => LogoutUseCase(sl()));
  sl.registerLazySingleton(() => RefreshTokenUseCase(sl()));
  sl.registerLazySingleton(() => GetUserUseCase(sl()));

  // Use cases - Content
  sl.registerLazySingleton(() => GetCategoriesUseCase(sl()));
  sl.registerLazySingleton(() => GetTopicsUseCase(sl()));
  sl.registerLazySingleton(() => GetVideosUseCase(sl()));
  sl.registerLazySingleton(() => GetVideoDetailUseCase(sl()));
  sl.registerLazySingleton(() => MarkVideoViewedUseCase(sl()));

  // Use cases - Subscription
  sl.registerLazySingleton(() => GetSubscriptionPlansUseCase(sl()));
  sl.registerLazySingleton(() => CreateOrderUseCase(sl()));
  sl.registerLazySingleton(() => VerifyPaymentUseCase(sl()));
  sl.registerLazySingleton(() => GetSubscriptionStatusUseCase(sl()));
  sl.registerLazySingleton(() => CancelSubscriptionUseCase(sl()));

  // Providers
  sl.registerFactory(
    () => AuthProvider(
      googleSignInUseCase: sl(),
      logoutUseCase: sl(),
      refreshTokenUseCase: sl(),
      getUserUseCase: sl(),
    ),
  );
  sl.registerFactory(
    () => ContentProvider(
      getCategoriesUseCase: sl(),
      getTopicsUseCase: sl(),
      getVideosUseCase: sl(),
      getVideoDetailUseCase: sl(),
      markVideoViewedUseCase: sl(),
    ),
  );
  sl.registerFactory(
    () => SubscriptionProvider(
      getSubscriptionPlansUseCase: sl(),
      createOrderUseCase: sl(),
      verifyPaymentUseCase: sl(),
      getSubscriptionStatusUseCase: sl(),
      cancelSubscriptionUseCase: sl(),
    ),
  );
}
