import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';
import '../constants/app_constants.dart';
import '../error/exceptions.dart';

class ApiClient {
  late final Dio _dio;
  final FlutterSecureStorage _secureStorage;
  final Logger _logger;
  
  ApiClient({
    required FlutterSecureStorage secureStorage,
    required Logger logger,
  }) : _secureStorage = secureStorage,
       _logger = logger {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl + AppConstants.apiVersion,
      connectTimeout: const Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: const Duration(milliseconds: AppConstants.receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    // Request interceptor for adding auth token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _secureStorage.read(key: AppConstants.accessTokenKey);
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        
        _logger.d('Request: ${options.method} ${options.path}');
        _logger.d('Headers: ${options.headers}');
        if (options.data != null) {
          _logger.d('Data: ${options.data}');
        }
        
        handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.d('Response: ${response.statusCode} ${response.requestOptions.path}');
        _logger.d('Data: ${response.data}');
        handler.next(response);
      },
      onError: (error, handler) async {
        _logger.e('Error: ${error.response?.statusCode} ${error.requestOptions.path}');
        _logger.e('Error Data: ${error.response?.data}');
        
        // Handle token refresh for 401 errors
        if (error.response?.statusCode == 401) {
          try {
            await _refreshToken();
            // Retry the original request
            final retryResponse = await _dio.request(
              error.requestOptions.path,
              options: Options(
                method: error.requestOptions.method,
                headers: error.requestOptions.headers,
              ),
              data: error.requestOptions.data,
              queryParameters: error.requestOptions.queryParameters,
            );
            handler.resolve(retryResponse);
            return;
          } catch (e) {
            // Token refresh failed, clear storage and throw auth exception
            await _clearAuthData();
            handler.reject(DioException(
              requestOptions: error.requestOptions,
              error: const AuthenticationException(
                message: 'Authentication failed. Please login again.',
                statusCode: 401,
              ),
            ));
            return;
          }
        }
        
        handler.next(error);
      },
    ));
  }
  
  Future<void> _refreshToken() async {
    final refreshToken = await _secureStorage.read(key: AppConstants.refreshTokenKey);
    if (refreshToken == null) {
      throw const AuthenticationException(message: 'No refresh token available');
    }
    
    try {
      final response = await _dio.post('/auth/android/refresh', data: {
        'refresh_token': refreshToken,
      });
      
      final newAccessToken = response.data['access_token'];
      final newRefreshToken = response.data['refresh_token'];
      
      await _secureStorage.write(key: AppConstants.accessTokenKey, value: newAccessToken);
      await _secureStorage.write(key: AppConstants.refreshTokenKey, value: newRefreshToken);
      
      _logger.i('Token refreshed successfully');
    } catch (e) {
      _logger.e('Token refresh failed: $e');
      throw const AuthenticationException(message: 'Token refresh failed');
    }
  }
  
  Future<void> _clearAuthData() async {
    await _secureStorage.delete(key: AppConstants.accessTokenKey);
    await _secureStorage.delete(key: AppConstants.refreshTokenKey);
    await _secureStorage.delete(key: AppConstants.userDataKey);
    await _secureStorage.write(key: AppConstants.isLoggedInKey, value: 'false');
  }
  
  // HTTP Methods
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(message: 'Connection timeout. Please check your internet connection.');
      
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ?? 'An error occurred';
        
        switch (statusCode) {
          case 400:
            return ValidationException(message: message);
          case 401:
            return AuthenticationException(message: message, statusCode: statusCode);
          case 403:
            return AuthorizationException(message: message, statusCode: statusCode);
          case 404:
            return ContentNotFoundException(message: message);
          case 500:
          default:
            return ServerException(message: message, statusCode: statusCode);
        }
      
      case DioExceptionType.cancel:
        return const NetworkException(message: 'Request was cancelled');
      
      case DioExceptionType.unknown:
      default:
        return const NetworkException(message: 'Network error occurred. Please check your internet connection.');
    }
  }
}
