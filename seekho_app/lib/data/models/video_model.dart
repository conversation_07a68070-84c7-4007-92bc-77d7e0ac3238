import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/video.dart';

part 'video_model.g.dart';

@JsonSerializable()
class VideoModel extends Video {
  const VideoModel({
    required super.id,
    required super.topicId,
    required super.title,
    required super.description,
    required super.thumbnailUrl,
    required super.videoUrl,
    required super.duration,
    required super.order,
    required super.isActive,
    required super.isPremium,
    required super.isViewed,
    required super.viewCount,
    required super.createdAt,
    required super.updatedAt,
    super.tags,
  });

  factory VideoModel.fromJson(Map<String, dynamic> json) => _$VideoModelFromJson(json);

  Map<String, dynamic> toJson() => _$VideoModelToJson(this);

  factory VideoModel.fromEntity(Video video) {
    return VideoModel(
      id: video.id,
      topicId: video.topicId,
      title: video.title,
      description: video.description,
      thumbnailUrl: video.thumbnailUrl,
      videoUrl: video.videoUrl,
      duration: video.duration,
      order: video.order,
      isActive: video.isActive,
      isPremium: video.isPremium,
      isViewed: video.isViewed,
      viewCount: video.viewCount,
      createdAt: video.createdAt,
      updatedAt: video.updatedAt,
      tags: video.tags,
    );
  }

  Video toEntity() {
    return Video(
      id: id,
      topicId: topicId,
      title: title,
      description: description,
      thumbnailUrl: thumbnailUrl,
      videoUrl: videoUrl,
      duration: duration,
      order: order,
      isActive: isActive,
      isPremium: isPremium,
      isViewed: isViewed,
      viewCount: viewCount,
      createdAt: createdAt,
      updatedAt: updatedAt,
      tags: tags,
    );
  }
}
