// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionPlanModel _$SubscriptionPlanModelFromJson(
        Map<String, dynamic> json) =>
    SubscriptionPlanModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toInt(),
      durationDays: (json['durationDays'] as num).toInt(),
      isActive: json['isActive'] as bool,
      isTrial: json['isTrial'] as bool,
      features:
          (json['features'] as List<dynamic>).map((e) => e as String).toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$SubscriptionPlanModelToJson(
        SubscriptionPlanModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'durationDays': instance.durationDays,
      'isActive': instance.isActive,
      'isTrial': instance.isTrial,
      'features': instance.features,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

UserSubscriptionModel _$UserSubscriptionModelFromJson(
        Map<String, dynamic> json) =>
    UserSubscriptionModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      planId: json['planId'] as String,
      planModel: UserSubscriptionModel._planFromJson(
          json['planModel'] as Map<String, dynamic>),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      isActive: json['isActive'] as bool,
      status: json['status'] as String,
      razorpaySubscriptionId: json['razorpaySubscriptionId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UserSubscriptionModelToJson(
        UserSubscriptionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'planId': instance.planId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'isActive': instance.isActive,
      'status': instance.status,
      'razorpaySubscriptionId': instance.razorpaySubscriptionId,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'planModel': UserSubscriptionModel._planToJson(instance.planModel),
    };

PaymentOrderModel _$PaymentOrderModelFromJson(Map<String, dynamic> json) =>
    PaymentOrderModel(
      id: json['id'] as String,
      razorpayOrderId: json['razorpayOrderId'] as String,
      planId: json['planId'] as String,
      amount: (json['amount'] as num).toInt(),
      currency: json['currency'] as String,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$PaymentOrderModelToJson(PaymentOrderModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'razorpayOrderId': instance.razorpayOrderId,
      'planId': instance.planId,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
    };
