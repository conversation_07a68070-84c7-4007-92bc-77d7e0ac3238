import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/subscription.dart';

part 'subscription_model.g.dart';

@JsonSerializable()
class SubscriptionPlanModel extends SubscriptionPlan {
  const SubscriptionPlanModel({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.durationDays,
    required super.isActive,
    required super.isTrial,
    required super.features,
    required super.createdAt,
    required super.updatedAt,
  });

  factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) => 
      _$SubscriptionPlanModelFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionPlanModelToJson(this);

  SubscriptionPlan toEntity() {
    return SubscriptionPlan(
      id: id,
      name: name,
      description: description,
      price: price,
      durationDays: durationDays,
      isActive: isActive,
      isTrial: isTrial,
      features: features,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

@JsonSerializable()
class UserSubscriptionModel extends UserSubscription {
  @JsonKey(fromJson: _planFromJson, toJson: _planToJson)
  final SubscriptionPlanModel planModel;

  const UserSubscriptionModel({
    required super.id,
    required super.userId,
    required super.planId,
    required this.planModel,
    required super.startDate,
    required super.endDate,
    required super.isActive,
    required super.status,
    super.razorpaySubscriptionId,
    required super.createdAt,
    required super.updatedAt,
  }) : super(plan: planModel);

  factory UserSubscriptionModel.fromJson(Map<String, dynamic> json) =>
      _$UserSubscriptionModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserSubscriptionModelToJson(this);

  static SubscriptionPlanModel _planFromJson(Map<String, dynamic> json) =>
      SubscriptionPlanModel.fromJson(json);

  static Map<String, dynamic> _planToJson(SubscriptionPlanModel plan) =>
      plan.toJson();

  UserSubscription toEntity() {
    return UserSubscription(
      id: id,
      userId: userId,
      planId: planId,
      plan: planModel.toEntity(),
      startDate: startDate,
      endDate: endDate,
      isActive: isActive,
      status: status,
      razorpaySubscriptionId: razorpaySubscriptionId,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}

@JsonSerializable()
class PaymentOrderModel extends PaymentOrder {
  const PaymentOrderModel({
    required super.id,
    required super.razorpayOrderId,
    required super.planId,
    required super.amount,
    required super.currency,
    required super.status,
    required super.createdAt,
  });

  factory PaymentOrderModel.fromJson(Map<String, dynamic> json) => 
      _$PaymentOrderModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentOrderModelToJson(this);

  PaymentOrder toEntity() {
    return PaymentOrder(
      id: id,
      razorpayOrderId: razorpayOrderId,
      planId: planId,
      amount: amount,
      currency: currency,
      status: status,
      createdAt: createdAt,
    );
  }
}
