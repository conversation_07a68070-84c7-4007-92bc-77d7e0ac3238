import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/user.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends User {
  const UserModel({
    required super.id,
    required super.email,
    required super.name,
    super.profilePicture,
    required super.role,
    required super.createdAt,
    required super.updatedAt,
    required super.isEmailVerified,
    required super.hasActiveSubscription,
    super.subscriptionExpiresAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  factory UserModel.fromEntity(User user) {
    return UserModel(
      id: user.id,
      email: user.email,
      name: user.name,
      profilePicture: user.profilePicture,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      isEmailVerified: user.isEmailVerified,
      hasActiveSubscription: user.hasActiveSubscription,
      subscriptionExpiresAt: user.subscriptionExpiresAt,
    );
  }

  User toEntity() {
    return User(
      id: id,
      email: email,
      name: name,
      profilePicture: profilePicture,
      role: role,
      createdAt: createdAt,
      updatedAt: updatedAt,
      isEmailVerified: isEmailVerified,
      hasActiveSubscription: hasActiveSubscription,
      subscriptionExpiresAt: subscriptionExpiresAt,
    );
  }
}
