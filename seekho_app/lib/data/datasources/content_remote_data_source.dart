import '../../core/network/api_client.dart';
import '../../core/error/exceptions.dart';
import '../models/category_model.dart';
import '../models/video_model.dart';

abstract class ContentRemoteDataSource {
  Future<List<CategoryModel>> getCategories();
  Future<List<VideoModel>> getTopVideos();
  Future<List<VideoModel>> getRecentVideos();
  Future<void> markVideoAsViewed(String videoId);
}

class ContentRemoteDataSourceImpl implements ContentRemoteDataSource {
  final ApiClient apiClient;

  ContentRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<CategoryModel>> getCategories() async {
    try {
      final response = await apiClient.get('/categories');

      if (response.statusCode == 200) {
        final List<dynamic> categoriesJson = response.data['categories'];
        return categoriesJson
            .map((json) => CategoryModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get categories',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to fetch categories');
    }
  }

  @override
  Future<List<VideoModel>> getTopVideos() async {
    try {
      final response = await apiClient.get('/videos/top');

      if (response.statusCode == 200) {
        final List<dynamic> videosJson = response.data['videos'];
        return videosJson
            .map((json) => VideoModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get top videos',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to fetch top videos');
    }
  }

  @override
  Future<List<VideoModel>> getRecentVideos() async {
    try {
      final response = await apiClient.get('/videos/recent');

      if (response.statusCode == 200) {
        final List<dynamic> videosJson = response.data['videos'];
        return videosJson
            .map((json) => VideoModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get recent videos',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to fetch recent videos');
    }
  }

  @override
  Future<void> markVideoAsViewed(String videoId) async {
    try {
      final response = await apiClient.post('/videos/$videoId/view');

      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to mark video as viewed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to mark video as viewed');
    }
  }
}
