import '../../core/network/api_client.dart';
import '../../core/error/exceptions.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  // Google Authentication
  Future<UserModel> signInWithGoogle(String idToken);
  Future<Map<String, dynamic>> refreshToken(String refreshToken);
  Future<UserModel> getCurrentUser();
  Future<void> logout();

  // Admin Authentication
  Future<UserModel> adminLogin(String username, String password);
  Future<UserModel> createAdmin({
    required String username,
    required String password,
    required String email,
    required String name,
  });
  Future<List<UserModel>> getAdminList();
  Future<void> removeAdmin(String adminId);
  Future<void> changeAdminPassword({
    required String currentPassword,
    required String newPassword,
  });

  // User Management
  Future<Map<String, dynamic>> getUserProfile();
  Future<UserModel> updateProfile({
    required String name,
    String? profilePicture,
  });
  Future<void> deleteAccount();

  // Android specific endpoints
  Future<Map<String, dynamic>> getAndroidConfig();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiClient apiClient;

  AuthRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<UserModel> signInWithGoogle(String idToken) async {
    try {
      final response = await apiClient.post('/auth/android/google', data: {
        'id_token': idToken,
      });

      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data['user']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Authentication failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to authenticate with Google');
    }
  }

  @override
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await apiClient.post('/auth/android/refresh', data: {
        'refresh_token': refreshToken,
      });

      if (response.statusCode == 200) {
        return {
          'access_token': response.data['access_token'],
          'refresh_token': response.data['refresh_token'],
        };
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Token refresh failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to refresh token');
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      final response = await apiClient.get('/auth/me');

      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data['user']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get user data',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to get current user');
    }
  }

  @override
  Future<void> logout() async {
    try {
      final response = await apiClient.post('/auth/android/logout');

      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Logout failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to logout');
    }
  }

  @override
  Future<UserModel> adminLogin(String username, String password) async {
    try {
      final response = await apiClient.post('/auth/admin/login', data: {
        'username': username,
        'password': password,
      });

      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Admin login failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to login as admin');
    }
  }

  @override
  Future<UserModel> createAdmin({
    required String username,
    required String password,
    required String email,
    required String name,
  }) async {
    try {
      final response = await apiClient.post('/auth/admin/create', data: {
        'username': username,
        'password': password,
        'email': email,
        'name': name,
      });

      if (response.statusCode == 201) {
        return UserModel.fromJson(response.data['data']['admin']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to create admin',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to create admin');
    }
  }

  @override
  Future<List<UserModel>> getAdminList() async {
    try {
      final response = await apiClient.get('/auth/admin/list');

      if (response.statusCode == 200) {
        final List<dynamic> adminList = response.data['data']['admins'];
        return adminList.map((admin) => UserModel.fromJson(admin)).toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get admin list',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to get admin list');
    }
  }

  @override
  Future<void> removeAdmin(String adminId) async {
    try {
      final response = await apiClient.delete('/auth/admin/remove/$adminId');

      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to remove admin',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to remove admin');
    }
  }

  @override
  Future<void> changeAdminPassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await apiClient.put('/auth/admin/change-password', data: {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      });

      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to change password',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to change admin password');
    }
  }

  @override
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await apiClient.get('/auth/me');

      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get user profile',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to get user profile');
    }
  }

  @override
  Future<UserModel> updateProfile({
    required String name,
    String? profilePicture,
  }) async {
    try {
      final data = <String, dynamic>{'name': name};
      if (profilePicture != null) {
        data['profilePicture'] = profilePicture;
      }

      final response = await apiClient.put('/auth/profile', data: data);

      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to update profile',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to update profile');
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      final response = await apiClient.delete('/auth/android/account');

      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to delete account',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to delete account');
    }
  }

  @override
  Future<Map<String, dynamic>> getAndroidConfig() async {
    try {
      final response = await apiClient.get('/auth/android/config');

      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get Android config',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to get Android config');
    }
  }
}
