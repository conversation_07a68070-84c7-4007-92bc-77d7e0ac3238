import '../../core/network/api_client.dart';
import '../../core/error/exceptions.dart';
import '../models/subscription_model.dart';

abstract class SubscriptionRemoteDataSource {
  Future<List<SubscriptionPlanModel>> getSubscriptionPlans();
  Future<PaymentOrderModel> createOrder(String planId);
  Future<UserSubscriptionModel> verifyPayment({
    required String orderId,
    required String paymentId,
    required String signature,
  });
  Future<UserSubscriptionModel?> getSubscriptionStatus();
  Future<void> cancelSubscription();
}

class SubscriptionRemoteDataSourceImpl implements SubscriptionRemoteDataSource {
  final ApiClient apiClient;

  SubscriptionRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<SubscriptionPlanModel>> getSubscriptionPlans() async {
    try {
      final response = await apiClient.get('/subscriptions/plans');

      if (response.statusCode == 200) {
        final List<dynamic> plansJson = response.data['plans'];
        return plansJson
            .map((json) => SubscriptionPlanModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get subscription plans',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to fetch subscription plans');
    }
  }

  @override
  Future<PaymentOrderModel> createOrder(String planId) async {
    try {
      final response = await apiClient.post('/subscriptions/create-order', data: {
        'plan_id': planId,
      });

      if (response.statusCode == 200) {
        return PaymentOrderModel.fromJson(response.data['order']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to create order',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to create payment order');
    }
  }

  @override
  Future<UserSubscriptionModel> verifyPayment({
    required String orderId,
    required String paymentId,
    required String signature,
  }) async {
    try {
      final response = await apiClient.post('/subscriptions/verify-payment', data: {
        'order_id': orderId,
        'payment_id': paymentId,
        'signature': signature,
      });

      if (response.statusCode == 200) {
        return UserSubscriptionModel.fromJson(response.data['subscription']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Payment verification failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to verify payment');
    }
  }

  @override
  Future<UserSubscriptionModel?> getSubscriptionStatus() async {
    try {
      final response = await apiClient.get('/subscriptions/status');

      if (response.statusCode == 200) {
        final subscriptionData = response.data['subscription'];
        if (subscriptionData != null) {
          return UserSubscriptionModel.fromJson(subscriptionData);
        }
        return null;
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to get subscription status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to fetch subscription status');
    }
  }

  @override
  Future<void> cancelSubscription() async {
    try {
      final response = await apiClient.post('/subscriptions/cancel');

      if (response.statusCode != 200) {
        throw ServerException(
          message: response.data['message'] ?? 'Failed to cancel subscription',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw const ServerException(message: 'Failed to cancel subscription');
    }
  }
}
