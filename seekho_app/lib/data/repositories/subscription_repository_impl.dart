import 'package:dartz/dartz.dart';
import '../../domain/entities/subscription.dart';
import '../../domain/repositories/subscription_repository.dart';
import '../../core/error/failures.dart';
import '../../core/error/exceptions.dart';
import '../datasources/subscription_remote_data_source.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final SubscriptionRemoteDataSource remoteDataSource;

  SubscriptionRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans() async {
    try {
      final planModels = await remoteDataSource.getSubscriptionPlans();
      final plans = planModels.map((model) => model.toEntity()).toList();
      return Right(plans);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, PaymentOrder>> createOrder(String planId) async {
    try {
      final orderModel = await remoteDataSource.createOrder(planId);
      return Right(orderModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, UserSubscription>> verifyPayment({
    required String orderId,
    required String paymentId,
    required String signature,
  }) async {
    try {
      final subscriptionModel = await remoteDataSource.verifyPayment(
        orderId: orderId,
        paymentId: paymentId,
        signature: signature,
      );
      return Right(subscriptionModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on PaymentException catch (e) {
      return Left(PaymentFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, UserSubscription?>> getSubscriptionStatus() async {
    try {
      final subscriptionModel = await remoteDataSource.getSubscriptionStatus();
      if (subscriptionModel != null) {
        return Right(subscriptionModel.toEntity());
      }
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelSubscription() async {
    try {
      await remoteDataSource.cancelSubscription();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on SubscriptionException catch (e) {
      return Left(SubscriptionFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }
}
