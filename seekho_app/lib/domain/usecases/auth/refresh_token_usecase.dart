import 'package:dartz/dartz.dart';
import '../../repositories/auth_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class RefreshTokenUseCase implements UseCaseNoParams<String> {
  final AuthRepository repository;

  RefreshTokenUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call() async {
    return await repository.refreshToken();
  }
}
