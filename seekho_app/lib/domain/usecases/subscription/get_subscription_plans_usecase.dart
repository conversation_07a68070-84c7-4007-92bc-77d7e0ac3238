import 'package:dartz/dartz.dart';
import '../../entities/subscription.dart';
import '../../repositories/subscription_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class GetSubscriptionPlansUseCase implements UseCaseNoParams<List<SubscriptionPlan>> {
  final SubscriptionRepository repository;

  GetSubscriptionPlansUseCase(this.repository);

  @override
  Future<Either<Failure, List<SubscriptionPlan>>> call() async {
    return await repository.getSubscriptionPlans();
  }
}
