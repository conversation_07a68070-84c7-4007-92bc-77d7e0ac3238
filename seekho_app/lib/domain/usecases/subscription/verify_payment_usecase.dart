import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/subscription.dart';
import '../../repositories/subscription_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class VerifyPaymentUseCase implements UseCase<UserSubscription, VerifyPaymentParams> {
  final SubscriptionRepository repository;

  VerifyPaymentUseCase(this.repository);

  @override
  Future<Either<Failure, UserSubscription>> call(VerifyPaymentParams params) async {
    return await repository.verifyPayment(
      orderId: params.orderId,
      paymentId: params.paymentId,
      signature: params.signature,
    );
  }
}

class VerifyPaymentParams extends Equatable {
  final String orderId;
  final String paymentId;
  final String signature;

  const VerifyPaymentParams({
    required this.orderId,
    required this.paymentId,
    required this.signature,
  });

  @override
  List<Object> get props => [orderId, paymentId, signature];
}
