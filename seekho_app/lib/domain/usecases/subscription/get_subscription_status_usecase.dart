import 'package:dartz/dartz.dart';
import '../../entities/subscription.dart';
import '../../repositories/subscription_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class GetSubscriptionStatusUseCase implements UseCaseNoParams<UserSubscription?> {
  final SubscriptionRepository repository;

  GetSubscriptionStatusUseCase(this.repository);

  @override
  Future<Either<Failure, UserSubscription?>> call() async {
    return await repository.getSubscriptionStatus();
  }
}
