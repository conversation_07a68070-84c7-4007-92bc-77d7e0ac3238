import 'package:dartz/dartz.dart';
import '../../repositories/subscription_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class CancelSubscriptionUseCase implements UseCaseNoParams<void> {
  final SubscriptionRepository repository;

  CancelSubscriptionUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call() async {
    return await repository.cancelSubscription();
  }
}
