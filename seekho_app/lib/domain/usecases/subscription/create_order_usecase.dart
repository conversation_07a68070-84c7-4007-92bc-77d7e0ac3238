import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/subscription.dart';
import '../../repositories/subscription_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class CreateOrderUseCase implements UseCase<PaymentOrder, CreateOrderParams> {
  final SubscriptionRepository repository;

  CreateOrderUseCase(this.repository);

  @override
  Future<Either<Failure, PaymentOrder>> call(CreateOrderParams params) async {
    return await repository.createOrder(params.planId);
  }
}

class CreateOrderParams extends Equatable {
  final String planId;

  const CreateOrderParams({required this.planId});

  @override
  List<Object> get props => [planId];
}
