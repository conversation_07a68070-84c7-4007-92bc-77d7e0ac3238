import 'package:dartz/dartz.dart';
import '../../entities/category.dart';
import '../../repositories/content_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class GetCategoriesUseCase implements UseCaseNoParams<List<Category>> {
  final ContentRepository repository;

  GetCategoriesUseCase(this.repository);

  @override
  Future<Either<Failure, List<Category>>> call() async {
    return await repository.getCategories();
  }
}
