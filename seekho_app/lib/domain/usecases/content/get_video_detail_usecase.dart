import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/video.dart';
import '../../repositories/content_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class GetVideoDetailUseCase implements UseCase<Video, GetVideoDetailParams> {
  final ContentRepository repository;

  GetVideoDetailUseCase(this.repository);

  @override
  Future<Either<Failure, Video>> call(GetVideoDetailParams params) async {
    return await repository.getVideoDetail(params.videoId);
  }
}

class GetVideoDetailParams extends Equatable {
  final String videoId;

  const GetVideoDetailParams({required this.videoId});

  @override
  List<Object> get props => [videoId];
}
