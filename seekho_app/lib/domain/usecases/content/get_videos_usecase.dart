import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/video.dart';
import '../../repositories/content_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class GetVideosUseCase implements UseCase<List<Video>, GetVideosParams> {
  final ContentRepository repository;

  GetVideosUseCase(this.repository);

  @override
  Future<Either<Failure, List<Video>>> call(GetVideosParams params) async {
    return await repository.getVideos(params.topicId);
  }
}

class GetVideosParams extends Equatable {
  final String topicId;

  const GetVideosParams({required this.topicId});

  @override
  List<Object> get props => [topicId];
}
