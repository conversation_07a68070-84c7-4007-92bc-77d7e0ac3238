import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../entities/topic.dart';
import '../../repositories/content_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class GetTopicsUseCase implements UseCase<List<Topic>, GetTopicsParams> {
  final ContentRepository repository;

  GetTopicsUseCase(this.repository);

  @override
  Future<Either<Failure, List<Topic>>> call(GetTopicsParams params) async {
    return await repository.getTopics(params.categoryId);
  }
}

class GetTopicsParams extends Equatable {
  final String categoryId;

  const GetTopicsParams({required this.categoryId});

  @override
  List<Object> get props => [categoryId];
}
