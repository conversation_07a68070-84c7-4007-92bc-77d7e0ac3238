import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../repositories/content_repository.dart';
import '../../../core/error/failures.dart';
import '../usecase.dart';

class MarkVideoViewedUseCase implements UseCase<void, MarkVideoViewedParams> {
  final ContentRepository repository;

  MarkVideoViewedUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(MarkVideoViewedParams params) async {
    return await repository.markVideoAsViewed(params.videoId);
  }
}

class MarkVideoViewedParams extends Equatable {
  final String videoId;

  const MarkVideoViewedParams({required this.videoId});

  @override
  List<Object> get props => [videoId];
}
