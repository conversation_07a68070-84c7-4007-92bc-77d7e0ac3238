import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String name;
  final String? profilePicture;
  final String role;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEmailVerified;
  final bool hasActiveSubscription;
  final DateTime? subscriptionExpiresAt;

  const User({
    required this.id,
    required this.email,
    required this.name,
    this.profilePicture,
    required this.role,
    required this.createdAt,
    required this.updatedAt,
    required this.isEmailVerified,
    required this.hasActiveSubscription,
    this.subscriptionExpiresAt,
  });

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        profilePicture,
        role,
        createdAt,
        updatedAt,
        isEmailVerified,
        hasActiveSubscription,
        subscriptionExpiresAt,
      ];

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? profilePicture,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? hasActiveSubscription,
    DateTime? subscriptionExpiresAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      hasActiveSubscription: hasActiveSubscription ?? this.hasActiveSubscription,
      subscriptionExpiresAt: subscriptionExpiresAt ?? this.subscriptionExpiresAt,
    );
  }
}
