import 'package:equatable/equatable.dart';

class SubscriptionPlan extends Equatable {
  final String id;
  final String name;
  final String description;
  final int price; // in paisa (₹1 = 100 paisa)
  final int durationDays;
  final bool isActive;
  final bool isTrial;
  final List<String> features;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationDays,
    required this.isActive,
    required this.isTrial,
    required this.features,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        durationDays,
        isActive,
        isTrial,
        features,
        createdAt,
        updatedAt,
      ];

  SubscriptionPlan copyWith({
    String? id,
    String? name,
    String? description,
    int? price,
    int? durationDays,
    bool? isActive,
    bool? isTrial,
    List<String>? features,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubscriptionPlan(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      durationDays: durationDays ?? this.durationDays,
      isActive: isActive ?? this.isActive,
      isTrial: isTrial ?? this.isTrial,
      features: features ?? this.features,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get formattedPrice {
    final rupees = price / 100;
    return '₹${rupees.toStringAsFixed(0)}';
  }
}

class UserSubscription extends Equatable {
  final String id;
  final String userId;
  final String planId;
  final SubscriptionPlan plan;
  final DateTime startDate;
  final DateTime endDate;
  final bool isActive;
  final String status; // active, expired, cancelled
  final String? razorpaySubscriptionId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserSubscription({
    required this.id,
    required this.userId,
    required this.planId,
    required this.plan,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.status,
    this.razorpaySubscriptionId,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        planId,
        plan,
        startDate,
        endDate,
        isActive,
        status,
        razorpaySubscriptionId,
        createdAt,
        updatedAt,
      ];

  UserSubscription copyWith({
    String? id,
    String? userId,
    String? planId,
    SubscriptionPlan? plan,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    String? status,
    String? razorpaySubscriptionId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserSubscription(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      planId: planId ?? this.planId,
      plan: plan ?? this.plan,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      razorpaySubscriptionId: razorpaySubscriptionId ?? this.razorpaySubscriptionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isExpired => DateTime.now().isAfter(endDate);
  
  int get daysRemaining {
    if (isExpired) return 0;
    return endDate.difference(DateTime.now()).inDays;
  }
}

class PaymentOrder extends Equatable {
  final String id;
  final String razorpayOrderId;
  final String planId;
  final int amount;
  final String currency;
  final String status;
  final DateTime createdAt;

  const PaymentOrder({
    required this.id,
    required this.razorpayOrderId,
    required this.planId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [
        id,
        razorpayOrderId,
        planId,
        amount,
        currency,
        status,
        createdAt,
      ];
}
