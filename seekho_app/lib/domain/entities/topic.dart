import 'package:equatable/equatable.dart';

class Topic extends Equatable {
  final String id;
  final String categoryId;
  final String name;
  final String description;
  final String thumbnailUrl;
  final int order;
  final bool isActive;
  final bool isPremium;
  final int videoCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Topic({
    required this.id,
    required this.categoryId,
    required this.name,
    required this.description,
    required this.thumbnailUrl,
    required this.order,
    required this.isActive,
    required this.isPremium,
    required this.videoCount,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        categoryId,
        name,
        description,
        thumbnailUrl,
        order,
        isActive,
        isPremium,
        videoCount,
        createdAt,
        updatedAt,
      ];

  Topic copyWith({
    String? id,
    String? categoryId,
    String? name,
    String? description,
    String? thumbnailUrl,
    int? order,
    bool? isActive,
    bool? isPremium,
    int? videoCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Topic(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      name: name ?? this.name,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      isPremium: isPremium ?? this.isPremium,
      videoCount: videoCount ?? this.videoCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
