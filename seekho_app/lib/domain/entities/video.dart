import 'package:equatable/equatable.dart';

class Video extends Equatable {
  final String id;
  final String topicId;
  final String title;
  final String description;
  final String thumbnailUrl;
  final String videoUrl;
  final int duration; // in seconds
  final int order;
  final bool isActive;
  final bool isPremium;
  final bool isViewed;
  final int viewCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? tags;

  const Video({
    required this.id,
    required this.topicId,
    required this.title,
    required this.description,
    required this.thumbnailUrl,
    required this.videoUrl,
    required this.duration,
    required this.order,
    required this.isActive,
    required this.isPremium,
    required this.isViewed,
    required this.viewCount,
    required this.createdAt,
    required this.updatedAt,
    this.tags,
  });

  @override
  List<Object?> get props => [
        id,
        topicId,
        title,
        description,
        thumbnailUrl,
        videoUrl,
        duration,
        order,
        isActive,
        isPremium,
        isViewed,
        viewCount,
        createdAt,
        updatedAt,
        tags,
      ];

  Video copyWith({
    String? id,
    String? topicId,
    String? title,
    String? description,
    String? thumbnailUrl,
    String? videoUrl,
    int? duration,
    int? order,
    bool? isActive,
    bool? isPremium,
    bool? isViewed,
    int? viewCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? tags,
  }) {
    return Video(
      id: id ?? this.id,
      topicId: topicId ?? this.topicId,
      title: title ?? this.title,
      description: description ?? this.description,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      videoUrl: videoUrl ?? this.videoUrl,
      duration: duration ?? this.duration,
      order: order ?? this.order,
      isActive: isActive ?? this.isActive,
      isPremium: isPremium ?? this.isPremium,
      isViewed: isViewed ?? this.isViewed,
      viewCount: viewCount ?? this.viewCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      tags: tags ?? this.tags,
    );
  }

  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  bool get isLocked => isPremium && !isViewed;
}
