import 'package:dartz/dartz.dart';
import '../entities/category.dart';
import '../entities/topic.dart';
import '../entities/video.dart';
import '../../core/error/failures.dart';

abstract class ContentRepository {
  Future<Either<Failure, List<Category>>> getCategories();
  Future<Either<Failure, List<Topic>>> getTopics(String categoryId);
  Future<Either<Failure, List<Video>>> getVideos(String topicId);
  Future<Either<Failure, Video>> getVideoDetail(String videoId);
  Future<Either<Failure, void>> markVideoAsViewed(String videoId);
  Future<Either<Failure, List<Video>>> getTopVideos();
  Future<Either<Failure, List<Video>>> getRecentVideos();
}
