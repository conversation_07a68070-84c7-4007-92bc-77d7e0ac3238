import 'package:dartz/dartz.dart';
import '../entities/subscription.dart';
import '../../core/error/failures.dart';

abstract class SubscriptionRepository {
  Future<Either<Failure, List<SubscriptionPlan>>> getSubscriptionPlans();
  Future<Either<Failure, PaymentOrder>> createOrder(String planId);
  Future<Either<Failure, UserSubscription>> verifyPayment({
    required String orderId,
    required String paymentId,
    required String signature,
  });
  Future<Either<Failure, UserSubscription?>> getSubscriptionStatus();
  Future<Either<Failure, void>> cancelSubscription();
}
