name: seekho_app
description: "Seekho - Video Learning Platform"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8

  # State Management
  provider: ^6.1.2

  # Network & API
  dio: ^5.4.3+1
  retrofit: ^4.1.0
  json_annotation: ^4.9.0

  # Authentication & Firebase
  google_sign_in: ^6.2.1
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3

  # Secure Storage
  flutter_secure_storage: ^9.2.2

  # Payment
  razorpay_flutter: ^1.3.7

  # Video & Media
  video_player: ^2.8.6
  cached_network_image: ^3.3.1

  # UI Enhancements
  shimmer: ^3.0.0

  # Dependency Injection
  get_it: ^7.7.0

  # Utils
  intl: ^0.19.0
  equatable: ^2.0.5
  dartz: ^0.10.1

  # Logging
  logger: ^2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  retrofit_generator: ^8.1.0
  json_serializable: ^6.8.0
  build_runner: ^2.4.9

  # Testing
  mockito: ^5.4.4

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
