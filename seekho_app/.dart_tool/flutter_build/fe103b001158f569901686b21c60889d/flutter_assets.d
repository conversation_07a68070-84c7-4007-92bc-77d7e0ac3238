 /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/packages/fluttertoast/assets/toastify.js /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/packages/fluttertoast/assets/toastify.css /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/pubspec.yaml /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.js /Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.css /Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/.dart_tool/flutter_build/fe103b001158f569901686b21c60889d/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.15/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE /Users/<USER>/development/flutter/bin/cache/dart-sdk/pkg/_macros/LICENSE /Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/development/flutter/packages/flutter/LICENSE /Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/DOES_NOT_EXIST_RERUN_FOR_WILDCARD886725970