{"version": 2, "files": [{"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/content/get_topics_usecase.dart", "hash": "6efd6afff19aaccc852530f65e32d151"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllinkdatalist.dart", "hash": "4e8aaa21ed0ade8f7601849694c81ce5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationscrollitempattern.dart", "hash": "a3ab60b19b4725b3ea1d1b0cb1c64451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/platform_interface/platform_interface_firebase.dart", "hash": "81402c8eea37df800d379c88bdcf6f44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/enums.dart", "hash": "0126627ff99705319e77e37504e41d22"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "44927d8a4e3825e7c3be0af91307d083"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/oleaut32.g.dart", "hash": "01776292c19efee31306cb0679ab3773"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/exception.dart", "hash": "e625c15c91736f7c029d22eaf13a9599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/version.g.dart", "hash": "679b8cdea258a721b151994c5c4aa9b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "9a977b88944bf59512e9d8aaeef93605"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/either.dart", "hash": "a1ed8964b1f413479f996fc98f498feb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "13b920f66eba39405ab6c5487e5fc3f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellfolder.dart", "hash": "4a3cc462f40576025abe65fff32c0e63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/ios_options.dart", "hash": "704d7f872888ec6e9697123a180fd95d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/wtsapi32.g.dart", "hash": "d165617d9abc7ef296c4fc875471fae7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/propertykey.dart", "hash": "9e8d52d54397c70b7c20ef9436ca38c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "f90fd4f8a9988f08157d132c23c8c08d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "c51e8dd4b4c6fc4a085adda93a75fdc6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/auth/logout_usecase.dart", "hash": "5e8ef6a9c54c28d48c65ab8689c23b47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE", "hash": "abb5a1fdfd2511538e3e70557aad0ba1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader.dart", "hash": "54fa8ef3118882b34ee76276accb2c10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/bthprops.g.dart", "hash": "cf53cbbbfbbbc6e5f3d0fab91b9f20a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/entities/topic.dart", "hash": "4fe42ecb57357946850f53a34f2a81da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/repositories/auth_repository_impl.dart", "hash": "6b1473a3f33e5d7cede5c72839499167"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/video_list_item.dart", "hash": "a92795b544426e444a78e7ae99728edb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemimagefactory.dart", "hash": "d04edc39b6d3477197606ec9c969e738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfilesenumerator.dart", "hash": "9834ef8e867f05559620160927fc6964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispnotifysource.dart", "hash": "32170db149f7552573abb976a101f186"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response.dart", "hash": "2a02594ad813d39d23460e2abfd2551d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement8.dart", "hash": "110d15e94f599373085aba42911eadcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "0c38ab3123facc4ec6f01ba31158c3ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/kernel32.g.dart", "hash": "f17de3d9e71117cf29f360cbfaab5348"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworklistmanagerevents.dart", "hash": "ef4187427d8c318086d02bedaaee9300"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "796af05466fbe319d5fc699b982ded0c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/material.dart", "hash": "f485bc1aa4fbdf87e17bfb8f80e39258"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/types.dart", "hash": "4a1d1bdbd4e9be4c8af1a6c656730a66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/inline.dart", "hash": "5b3e856525ee8046183fed46364f86e9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ivirtualdesktopmanager.dart", "hash": "ffd004f95154cc4fe026271fb8aed8cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensordatareport.dart", "hash": "2fc9d0ed6032b1e7f7c310e4bff73f23"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtogglepattern.dart", "hash": "1afa24c132e0ed2df8a83e7de36112e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "0119e0f7758ee8ef19baeae2b96cb389"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "72c318c3499a7a4d533965d32c6dface"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/screens/main_screen.dart", "hash": "1560c4634181655efe8b3836944f2d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_android.dart", "hash": "23149dd1dabb201f41ccacb25e322741"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/apple_options.dart", "hash": "d4efda9ec695d776e6e7e0c6e33b6a4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/utils.dart", "hash": "599be812b0d48a34af027e2c896771e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/crypt32.g.dart", "hash": "032051419cbe678eeb63004a5ffbcfda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/lib/google_sign_in_ios.dart", "hash": "3c7e79576dd88fbfb5ec975f86e6f10e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement3.dart", "hash": "d1d288dcc528cf44d85a1788108201b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "2cb8483d7aa2b998d4641e25a0425f67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellchecker.dart", "hash": "f9114e11eefdb88a452875b8d583e267"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "49194534260502aa020910c20fb3ad6a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "625b858bd9847eab75d2f3f6295a25bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationgridpattern.dart", "hash": "1183f96a464afb812132566262172088"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation.dart", "hash": "a4282bea82459c80d5a209b34f6b6086"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/entities/user.dart", "hash": "042631732500b2c78c5d7890cb1e25b8"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/screens/home_screen.dart", "hash": "21c046bc734ff3fa6a4b5854446bfa27"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "f1c0b135f35af022771e30409953e0f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/lens.dart", "hash": "be965144cc24996fbab4430007fdb522"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable.dart", "hash": "52138432903419f8457bcad45e5e6e99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "17a28a030318e2c8f8fd653e0b862d50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/comdlg32.g.dart", "hash": "5f5e77b562118490a9610f89d30b3990"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationannotationpattern.dart", "hash": "1c877d25a9c369ae60db386f8a1a35ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/options.dart", "hash": "fd4b31aeef96e63881bfcd44031ae269"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/LICENSE", "hash": "********************************"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "05c5ca73bc4e912f53a324cfa508bbfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/ivector.dart", "hash": "2c3b23efc97d6a89005695c9035aefb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader4.dart", "hash": "5a65f8839771af0fad5b2cf647703264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemhiperfenum.dart", "hash": "adebe1537e162fcbe4404ab29e94fef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/src/event_emitter.dart", "hash": "a572ca46ecfcd599aa868c105c460144"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader7.dart", "hash": "a60dd773b7d69b347521fb64257f9397"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "1603f38e802a78686ee48e3554da22f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "160e007517eb9af8299b242a217c6ff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataassemblyimport.dart", "hash": "2c79ebc319fae192d118d1483a0a0b07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellcheckerfactory.dart", "hash": "502de1243ccffce33e394a25f669b96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/firebase_core.dart", "hash": "c83f257da86decec9584473cf41f0ee0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iprovideclassinfo.dart", "hash": "874923f8d9b00546393faf2d37e102ef"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "ff7c5f41b6493392c45ef30383f6af9b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "e138cb83b907c09a4ac468dff69d43de"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7b71540e417e6ea3f1134b4b677e0624"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/src/method_channel_google_sign_in.dart", "hash": "885d694fb04c2508328d3050fc2f2518"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "d3abf203392ec29c7ebbda6b41360d2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement4.dart", "hash": "90ad18cc7b92f34e912a56b5730afa4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material.dart", "hash": "ff1b06a4c51e36902ef2e5cf96495fea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/ilist.dart", "hash": "f88f3fbc4214b37c7ed8dec53bc2c3c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file/io_multipart_file.dart", "hash": "8094c68b4a15e6f73e09501aa6ff4a47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "006c00513de6bd421565ec6ffd776337"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqflite.dart", "hash": "f7be2d6ca06ea6e4deeee0e441b71d6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/utils.dart", "hash": "f5e920fcc3b932c9b189f55e8839ff02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio/dio_for_native.dart", "hash": "6f053637ded96c67b342e6a80e7372f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "2539eaeb4e2f2f69f678fd850c2332e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "38c6297c7e2085554452d28299d29a09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/filetime.dart", "hash": "3c1a4805a8645f3bdc5369321eddb32c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/monoid.dart", "hash": "95268e0f66551724de26c8afe0ead439"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/providers/subscription_provider.dart", "hash": "13a52a894888d127a78c235a1d569a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "ffc66c213d3e015ff3e03298622c7141"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationrangevaluepattern.dart", "hash": "844c6caebd7d2ff68149b6881deae9f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement6.dart", "hash": "91ddaac3aa28c7527659e459c7c60e68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/LICENSE", "hash": "2aacbb22761fd3ed84696ff1ecfb38fe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumidlist.dart", "hash": "d404d0c85a223afe9402bc0c33f60886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/network/api_client.dart", "hash": "adb17522ab02354ba2ffab597c4e7f11"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8199cdd8c075bef2ed0811394702680d"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/subscription/cancel_subscription_usecase.dart", "hash": "13f63140ecd6f1b398707c3210976cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/unpack_utf16.dart", "hash": "cfab296797450689ec04e7984e7d80e3"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/search_bar_widget.dart", "hash": "ff1ae3c22cacb55aacf7c83dedf37507"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/LICENSE", "hash": "80ae6870ab712d32cc9dff7f6174b603"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5ac341d21fd38e1a3307100a5b3c3138"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumresources.dart", "hash": "01f72c7d1903117cf6423e4a5e5916eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib/video_player_android.dart", "hash": "91ec31aa64426df54b3c81744a11bb38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensormanager.dart", "hash": "d1ed08ede0f2e8ca979d575b3b6b8c64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatadispenser.dart", "hash": "3fc24d3b43ff4a6b63811978cfb697e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "577ec098e9f6651d7704fad48b4dd44a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/firebase_core_exceptions.dart", "hash": "bc949707cfd60ff573b48a27b02f6756"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/flutter_secure_storage_platform_interface.dart", "hash": "8dac3815609f98dfefa968bc2ea4a408"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellingerror.dart", "hash": "6338f981e88891ddf1b5674bc7baac0e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "ed582bff49cac60fb08ccee9ccc7c573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/src/event.dart", "hash": "5030c407565889225a0f5dc11c8e0b8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_level.dart", "hash": "a89825cb544a7bf5ead83ab803a426e0"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/video_model.dart", "hash": "0ab13e6db05f5e8ed6ca6d3b1e406679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "9d2e926705e7e23b2e34aa022cf55324"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "/Users/<USER>/development/flutter/bin/cache/dart-sdk/pkg/_macros/LICENSE", "hash": "80ae6870ab712d32cc9dff7f6174b603"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetwork.dart", "hash": "d74c6cdab75d5d9b62ebf9cb07182a26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "0201ee9c8aee2bb24db2c74b6c0cd485"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialogcustomize.dart", "hash": "3308bebb4908701a4b9fea36a346ff20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "e3b1d07a31d08470207f2b668564a833"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "3f7c50b425818ea563c8459cfd6f9d5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "cb45dd3f32378f0acf6b8a514cdc6084"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "2fe7a1026669f97031a83f6da44d248b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/combase.dart", "hash": "90ed8a12c97e362a162da690203df055"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/datasources/content_remote_data_source.dart", "hash": "62b83865422ece531fd71641251435ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ichannelaudiovolume.dart", "hash": "1d94fcee85c9891374166b09889f8f09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworkconnection.dart", "hash": "cd2e632e852f1e82cc2084c49e16500a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib/video_player_avfoundation.dart", "hash": "a8fed2f60da802923f5c87a2555c70a8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumnetworks.dart", "hash": "dabc834de271cc395f1aa1b0e9484fc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_comm_l1_1_2.g.dart", "hash": "27a9e4cafdd931f481840ea15b053cfb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "737642bf1a2d9ebd63c82016292b6b93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_config.dart", "hash": "e0f2b097829216421823bda9ec381cab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/function.dart", "hash": "feb982bbc2792cca604bae81f1bf4d40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "3ecea4d9c25299b0ea66c58256909437"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/.dart_tool/flutter_build/fe103b001158f569901686b21c60889d/app.dill", "hash": "6b1f8660487a1ebca7109b1247c1c583"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/free.dart", "hash": "54c210908f9d1c98aa616dea555e2869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immendpoint.dart", "hash": "1f7230510c62078bbebc01fa06f1fb8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation3.dart", "hash": "befcd208b8f5a4c002c3f3fcaa09ccfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdevice.dart", "hash": "19cbf88ec406061bbb2e9260b18510a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/entities/category.dart", "hash": "5c786324ecd6fb7b2d9ba66a54bd7c77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "f6816ebd27db772616d01f543b33d0f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtransformpattern.dart", "hash": "d2659325876491c5e79bf348a57ccb66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/irunningobjecttable.dart", "hash": "62c7a9476527a2724b820dc7a40a5105"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "ced9d2439e23015bfc2bac438f598985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcondition.dart", "hash": "0469c2fefb6084f264cd0df8bce7263a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "f979a94d7bd35cf2a5168fbfb9bdcf1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/options.dart", "hash": "e64d63aabc0975a7e9fdb384598c2f8f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/xinput1_4.g.dart", "hash": "aa553bed1d888de4b53878cd701f022f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcacherequest.dart", "hash": "4d161c7f1cf07d8222224f941e4241ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/_image_loader.dart", "hash": "af9339e8836ca91cbc9c8fd6b2de7cc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/LICENSE", "hash": "7b2b74124c0fcf7df0c0b4605f9cac61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-8.0.0/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "8e7a18cd739e24a264facecc38379085"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants.dart", "hash": "a32a056475fc5b8abb7b5c6ca6c9d9b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_string_array.dart", "hash": "dce5e400c1f0958583196f9db05de7b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "e5163b554926dc261b556dc5d94245d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "15059e9824dd4a9e06136d8dfd91c26a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iagileobject.dart", "hash": "4bc403cec1c5846051bca88edb712a8c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllinkdual.dart", "hash": "8a9ddc700c157e2eed7422823ba8f6f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/dialogs.dart", "hash": "3deaa1966e0bb94f98b6f402ad576915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/setupapi.g.dart", "hash": "875f7a3b998ac288c00e9f1edeb69342"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "16903e1f0bc6b66d30a5804b7ae71fe5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "5af6304445e6664f6caca9ed4b5e885f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/src/listener.dart", "hash": "95c953a68559412fdca689ca793e9d8c"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/.dart_tool/package_config_subset", "hash": "8c5119e8ddfeee0a568285d6eedd5d20"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "7f37ea646d3e1b9f923f3af623128a0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformer.dart", "hash": "49dba21de16234aaed28f8fd898543a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessioncontrol2.dart", "hash": "63b3745a751f980bb7dabdd60ca65969"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_utils.dart", "hash": "bf850e483673d93e76e1fd5c69d8135a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7b28ec35aed9cbc3319bf4c15d7b352a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iclassfactory.dart", "hash": "adbacdd68acdd5e35ce91a3475a1be38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdroptargetpattern.dart", "hash": "a54cd317ab692f04c4fc26e8cd9cfce5"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/datasources/subscription_remote_data_source.dart", "hash": "a33ebbc427ee3f8b72ae9cfac2003bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/subscription_model.g.dart", "hash": "438be5b31a4d29dac4d51f58d85f2ba8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "428549777327ddf7f2287b69cab7b68b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.css", "hash": "c4da0fc89c24df5cc6a180d9ac851706"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemconfigurerefresher.dart", "hash": "313def06f0e97cc30450b2ac2d1cdfdb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isequentialstream.dart", "hash": "2fd375e07e198a53eb645fb0e05de9d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "5aa32c5e6b696b66556b4f91bf5983a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensor.dart", "hash": "e78db72a9d4ba4e067c0afb9ab7c4518"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/error/failures.dart", "hash": "681dbf73a854c4cd102b305126684d5f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/powrprof.g.dart", "hash": "b561eb6b0b72c619394c33c55b2e8301"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "31db92b0b980a193d02b613bb9c0f819"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialog2.dart", "hash": "b44f7c15167b08017c2831fc2ea95c1e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "dcb5ce635282f4390eca8dcb73737991"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtransformpattern2.dart", "hash": "79a8e92d9a9d2929d6f432a49ce22c21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winmm.g.dart", "hash": "f81746c322f4a5269713665a1c9ab0fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "98f06a29791e4f6ffc1ccefd18f323fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/foldable.dart", "hash": "4b7771bcf6cf3f8ddb200d8c7574bad6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataimport2.dart", "hash": "40ffb3e4d1023f052603c4ced82ef3a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imodalwindow.dart", "hash": "69cf74c98799489b81531323ecc6cc74"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "23f5fb6033bd02c94d263d1ed41fb90e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/state.dart", "hash": "3b6cf754a72fb95b1994c40d7cb2f896"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/google_sign_in_platform_interface.dart", "hash": "0ce7da9a8c9c120cffa630447a0b9876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/synchronized.dart", "hash": "b62a2c91536fb75946b3193c9a5b04aa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/idesktopwallpaper.dart", "hash": "80dcf36840d009af3563b72ef07995f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "39e18667c84e363d875147cc5dc6b2fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "0f6f972f6232b9d18cf00a9fa432127b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/typed/stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/category_model.dart", "hash": "51d61d1654bd61621ec37e27c5cda300"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "27a4ea7d50fcfd776a5d69fce0cd26ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/user32.g.dart", "hash": "cfcb95b36d6698367184c63483b98f9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "1d893e6d648c41d8e3281a76a2320431"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumstring.dart", "hash": "3f9dcf46067e3b9d3f41240d43c1c59c"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "95e0f07e94b52d619b3e4c031087dab4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/main.dart", "hash": "3e9ba69c5764a4eb9d36da3df1245d70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_string_l1_1_0.g.dart", "hash": "acb5f13d5c2cb9a6c302487169ead3dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/fused_transformer.dart", "hash": "4cbacf46dc43afb0d059b0016010f45b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "a108c1a02c56f9162ede59a7c30ed41d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuri.dart", "hash": "3dd3dfc2d8c37cb47724659f8d2098e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/id.dart", "hash": "4a697797b0543b0f88493a18d54c6282"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imoniker.dart", "hash": "7be696b635be2ab16fad5ea6c723dd1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_shcore_scaling_l1_1_1.g.dart", "hash": "456c173e6b3ea24a522a57a8bcb3c0e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "3cb88cf9e4198e0d510b78aa005aa597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/video_model.g.dart", "hash": "419bce73b352224e3fcd5f5265d1d485"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib/google_sign_in.dart", "hash": "54834f5c04890b0aa881b46287a7281b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "fc0b4ef021be19542435a86743d8de7c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "3d7501e746aaa83cd9cc1b508d3f7ebe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/ole32.g.dart", "hash": "ae656f0ed8c596c679f657c63db1697e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "b3465d5b02dd4743d8d9f9e4170a1151"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/LICENSE", "hash": "fde2b1b7d744e3606529be50acb7fded"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/imap.dart", "hash": "ae031f6e52d65f28f7eb1515fc4ff111"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatatables.dart", "hash": "9aaf9cf926a4121e7dad07c34de018d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retrofit_generator-8.2.1/LICENSE", "hash": "eec8df566dadd4c873d9e323a7e31bd8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "9e64d24aeed0ce5534422c6e4b454676"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestospackagedependency.dart", "hash": "2880c8f7069a05a0e91104119bd918c4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationandcondition.dart", "hash": "cdddb39c933c3ad3633502639963a480"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/LICENSE", "hash": "2ca873395712084d52af72aa1d4a2b80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclock2.dart", "hash": "286726a4ae635c3cb149cd640c3c096f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/ihashmap.dart", "hash": "c210d5cb392a28576209c56e0742a93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellservice.dart", "hash": "bde779c0e81c5e48653c2dbc3d589cd3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "59cca02e92c0ff79aac0c54c50e3bd2b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationwindowpattern.dart", "hash": "58739f2e107e62b5568a115d4bd0d523"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/shlwapi.g.dart", "hash": "44636e7c7555564a463937cfb744b79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/macros.dart", "hash": "8016baf49ccbce205455e3fc0bddbb17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestapplication.dart", "hash": "bc01545a1cca050f2067c0b6163a4755"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader3.dart", "hash": "e97932f0cef53e2c018203ac3cf1c7e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "f9646c35238459f46dd9d87783813f08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/log.dart", "hash": "a7730cdfe094a3fdd076fcf5fe39ed65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptor.dart", "hash": "9c6333c301963385de32595f9442d6ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "e38cc213f0e4b4ed76471f4d70e20abe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "2de077d432c4bb0a9525e9ab5d84913a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/inetworklistmanager.dart", "hash": "853dcca1f602ac69e366e5d6afcba80e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellcheckerchangedeventhandler.dart", "hash": "79ad9ffc538ede29c2cc688c386d717b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemclassobject.dart", "hash": "e1908e595346f6fa2b9cafaf3c4f4b15"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/scarddlg.g.dart", "hash": "f610f248b9a4132a9a920ccbf1d3cc5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiostreamvolume.dart", "hash": "9c9f9bcb5f1fed59a9b35912bbd2cf56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxpackagereader.dart", "hash": "59137da0b55aefe8a4074891792a55b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "ebc6759fa73c53bc12d581d9e1e4c821"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextpattern2.dart", "hash": "1dfa85bd16bf08ae91f9cceb02ef1563"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iknownfolder.dart", "hash": "6f95956e8269ea0bfb393ad11311b570"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationcustomnavigationpattern.dart", "hash": "84de591d644b29f5e21052bd9c84263c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/linux_options.dart", "hash": "26c4f0c369b83e53900ac87bf7e0dcff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "e85b30de1963bb6981d72b6027a66dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/dispatcher.dart", "hash": "cf07a18bf186a6805d8c384420518376"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/response/response_stream_handler.dart", "hash": "87061e866d20d4a66d6990c36638681f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/advapi32.g.dart", "hash": "ca80e89c29289e7bd0153b09a83813f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackagedependenciesenumerator.dart", "hash": "0350d89163d2f0ced1ab36a631c63fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionenumerator.dart", "hash": "379b3eed65ef945d342de0a84831c177"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/flutter_secure_storage.dart", "hash": "5a944801c9b2bd3447f982168b31e46c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "75fa80ab7762b14e35b11b93da96d4a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "74902317f9caa3ba9c05b114d45d8a02"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/repositories/content_repository.dart", "hash": "5c984e16d07813bab3f20430e1df72f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/firebase_options.dart", "hash": "3776c53b4b5546b121422c8c43cbcd7f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/entities/subscription.dart", "hash": "1760e69989d45ce69218f4d6db664f33"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "0f61d8c0c0870ae724b64f2f2af816bc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemrefresher.dart", "hash": "5026f3bc8f63a10ffb208a35e304f40c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemresources.dart", "hash": "3546603acafc7773ff75c32041c86fce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/di/injection_container.dart", "hash": "cf9e31087e52291762b3dad45490e8fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iknownfoldermanager.dart", "hash": "76c591c08a5c91070981e5e18d21a645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/enums.g.dart", "hash": "ebee8885b5afd397cfa8920eeccf88e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationitemcontainerpattern.dart", "hash": "17cf81dd718b76ea3b1453b5f74e1cd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationvirtualizeditempattern.dart", "hash": "34ac34257c6ee30da8c0b6de4d0a5444"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "fad2940dc1f4f3e4a0ebb5c7ff40a3a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader5.dart", "hash": "85574281bf7d7bee9722a21e092b4be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.15/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader2.dart", "hash": "9e2940d007af19bd5cf177e3be339363"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionmanager2.dart", "hash": "abafcf39ea8af4f661e2141f3a4607e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib/src/messages.g.dart", "hash": "172f0b2356a6692d7db3ddeb623d2038"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechvoice.dart", "hash": "de4097d4b754d587b6c15dec5805d0e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeventsource.dart", "hash": "cd336df9936bd016c7eeadf2ef3b4ee3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/traversable.dart", "hash": "34ed5865eab973572d06b84eda5fa0a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "a55ac84003178cdc783ca41a634500a8"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersistmemory.dart", "hash": "4beadd212378edebcbcbd4f68f9a183a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/test/test_flutter_secure_storage_platform.dart", "hash": "362bf1b65ae84f1129622a8814a50aad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "7b70bab5ac21ac24af3c971965617277"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "490fffadb78eb29c5fe209be7fe12370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifileopendialog.dart", "hash": "79e42699e1c911b6f1ad9e3d54fdb3e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "57b508bc908fd0950889e1d70ce36fdd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/callbacks.dart", "hash": "b020749262d0d602700cd21e6f41acdb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib/src/method_channel_flutter_secure_storage.dart", "hash": "20e7221c12677486628b48b0c30569f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/eq.dart", "hash": "d1e24ae6d07c3bd2a0c52002b0811bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "737365e0b93f911e49f1ac1e5363564c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "b0aac7d00e469646d25550d1e4e77d12"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "5638f5f2028c522b32626825f6bd5b7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange.dart", "hash": "73aa79d0da8711d3fe796ddbfb5d7585"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_io.dart", "hash": "7d313ac68ec3f410b31e39f450fdaf0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/irestrictederrorinfo.dart", "hash": "5a217487ee7b63aad11dcecb6a1ef25b"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/content/get_categories_usecase.dart", "hash": "a8823d8dc153e2ca4fb53451f728bf5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdevicecollection.dart", "hash": "7c246a3f46fba3b11765f3b103095947"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9af22b49fd7407bc0ef05667f139defd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "726a60283ea6c3a38fbb1ea6139cb4f0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dxva2.g.dart", "hash": "4725ff0ec5208552bafe4d6459df82fa"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "0434e70443094435172ff3d214d26bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "893548eaf87a8fd903da6fa761ad5ec1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifilesavedialog.dart", "hash": "dbf08eefdba5228054f4ebc6e6833c17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "b8c09bf358fcebf2f4c9214d1007536d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "222bc785bdb07236eb8746bafcb02592"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/LICENSE", "hash": "786b40670961113fd02b3fb3c0165c60"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "0bb85eff209a2008dc5f47b2beda5bf3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib/get_it.dart", "hash": "e6f649d6b0da154792babc16e0e5d02e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_comm_l1_1_1.g.dart", "hash": "33f4d3cc878470ce68dbb3087c0cf584"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/repositories/content_repository_impl.dart", "hash": "7559b06f6693565cb8d91e50ba8fee37"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "9bf11cc1ea784a251bf67350f02f910f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/bluetoothapis.g.dart", "hash": "dee1906acf6e5639024770351149df59"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/subscription/get_subscription_plans_usecase.dart", "hash": "d6a689740efcbb7d8a25a72bf75e3399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE", "hash": "274291edc62b938ad94e61cec4a14bec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/functions.dart", "hash": "e0123a0c77b03d27b3e2e899764c8d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/trampoline.dart", "hash": "dc731aa2f4f8eaa8342f4c44145b787a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement2.dart", "hash": "b84e7e0b89d2bad9ce9064ec3afc6119"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "6e9f863d0492f0f28bab13c40e44f427"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "12a21ff35182c138908274c8b66714d9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifileisinuse.dart", "hash": "704fe549f6257bb0025634b59ed121a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/progress_stream/io_progress_stream.dart", "hash": "6ea89c3bc6b0860bd7c16998d3950c3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/repositories/auth_repository.dart", "hash": "3f1db46d506f13d4ad2f7fbf770690eb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "3303320b233b1ca33a9e6e8c93e2d2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/video_carousel.dart", "hash": "df3fce8f58b15d48e23be7e66f553d9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "782760e5709624f38ebac3b7c728a792"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrangearray.dart", "hash": "91c64b5db3c0685967d4a0ce319c5b59"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "c2e0fa3415ed461288b6e2aecf569919"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "34371da200382409d181bf9c3fcaefc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/structs.g.dart", "hash": "557ed823467a80fba26020510f836870"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio.dart", "hash": "3467899798f7f8ca82797ccde4772534"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iconnectionpoint.dart", "hash": "81f755ba6336a4b4cfc0e376b4f8244d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/winrt_helpers.dart", "hash": "8a032ca2b66b8be21ce8368f80406db7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement7.dart", "hash": "7b704b062a8ddbec0176222cf280563c"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/subscription_model.dart", "hash": "0d7df08a8c876444fc2b15cd9e964b42"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackageid.dart", "hash": "6216df76c2226292e1e6c05135e22217"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/dio.dart", "hash": "3059dceae50124dbd966f136c80016fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechobjecttokens.dart", "hash": "d2026a23132fde8bda02e43a141f020b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "88dbcce51623c5bb2cbe1e4a0f80a902"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "bef4f4d150af7d7e46b13da4847f86fa"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/firebase_options.dart", "hash": "09c9f922da19d88bf9f5e8a26dc47a4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/pigeon/messages.pigeon.dart", "hash": "27609fef75714481627c2ef33c2eb952"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/.dart_tool/flutter_build/fe103b001158f569901686b21c60889d/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/free_composition.dart", "hash": "4e1f4ae58dced4acf14ca16830f663f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ierrorinfo.dart", "hash": "30b1813d0f71eea6cae262f843f196a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/macos_options.dart", "hash": "ef56d0c30c2ebbf770de5c7e9cd6f6a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib/src/common.dart", "hash": "3dcff929b4744b138e66d1e26811a5fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "0e708f7885d57fccc31cdb5020c2d9c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iinspectable.dart", "hash": "c66a0a686960f09a12ed2eb99e4ad3c9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/dartz.dart", "hash": "51f75ffbe1063d646fe4be68ea0ec18b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4895dd7c08da98c883cb21943f4ca4d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2906cf9308cbed8eb54ab1638dd5f56e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "b3f8f8ba0560319908ddb5d9480a5788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "a732cf9cb336d70db5c1145f0e468953"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "e892b3496135877dd5a0ea2ea2fc91e8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "10505aa641207501d9a0759bf2d6515e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "e27d4685e9e6aa906547a77095cc1ac5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement9.dart", "hash": "5c637f86e1850037099c738512c748c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestapplicationsenumerator.dart", "hash": "c4029d239f6cfaf6d919757bbd59b403"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/screens/plus_screen.dart", "hash": "4d41ff3b169f3330cf8ef6adcf02e058"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemarray.dart", "hash": "1f105c42a107237821eb50094abf6b4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationscrollpattern.dart", "hash": "6f2c0852f8ccc6be4e3462b28b0e105f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/win32.dart", "hash": "e3851989de381ed948b1c290a299a2c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestproperties.dart", "hash": "25ff828118233f5852e97c3e15c2a5da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationboolcondition.dart", "hash": "276c1bd3d367490604e56ce50f55068b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/endo.dart", "hash": "7bae050cc738669689f4eb37d3833102"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "0e0b94d805e193b69802ca99d5a51b27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/auth/get_user_usecase.dart", "hash": "0a0dab5eae1bc6a4be185842e83d9409"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_mixin.dart", "hash": "0f5d8dd74761633229f5cf2fd6358e05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/windows_options.dart", "hash": "b4355b7f9f9e50017ce52a8bda654dd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/lib/eventify.dart", "hash": "602cde6d39e1b1cf009147ac03e5e366"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "22cb97b7d09f329bab7ed148b4d181e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationobjectmodelpattern.dart", "hash": "3538e2e0fb5922d0a2639ae21ff1345f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/_internal.dart", "hash": "ef4618b5bf737a7625f62d841127c69d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_sysinfo_l1_2_3.g.dart", "hash": "4958f9fde0e398a31f8adfff89caf4d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_apiquery_l2_1_0.g.dart", "hash": "43bf805d1c539a8158296b167f65064c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechwaveformatex.dart", "hash": "95efdbdf332246f8a996a1219ed4a578"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "6ea409faabc2d30760053a8936e45796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/LICENSE", "hash": "2abd2c9a42d4caf2b4f1640d68b02fd5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipropertystore.dart", "hash": "54900b8b687e3ae6fff475cdc4a79ef0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationstylespattern.dart", "hash": "de4c7b647a0bbf39c8873977902389f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/cancel_token.dart", "hash": "c9f037c19c2e4adfe331d9e56f2e72c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/web_options.dart", "hash": "7dff3a0a1b5652f08f2267907c79844e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/list_to_blob.dart", "hash": "56d7144236503f311a7d9a966eaf2fbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumspellingerror.dart", "hash": "5f3836dd0624aa9d429b6a4d5b9d0321"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/order.dart", "hash": "676793176d8988affa626cef1d91dfd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winspool.g.dart", "hash": "21471f284b19e2a50238800becdb7419"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/pubspec.yaml", "hash": "43823f7d5279fca05cd309fba72ea464"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationspreadsheetitempattern.dart", "hash": "7c279a8dd6f509d8f7a9c5a29ea35852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclientduckingcontrol.dart", "hash": "8b1a62bc210bc087059381bbe1afa188"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelementarray.dart", "hash": "6c217e68a4edaca67f7c4cf9b53a2638"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib/get_it_impl.dart", "hash": "f37fa9cba6ab977f440c15a56b7dae4e"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/content/get_video_detail_usecase.dart", "hash": "04bc8de57e5907b366b84d9cc862b83a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "203fbbac922589879ae44083b04a368b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumnetworkconnections.dart", "hash": "e34d7dd943080889020806fbd56e32d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation2.dart", "hash": "db81a2e27f649abaf9de88c1f92b2686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactoryentry.dart", "hash": "52312cfbaf36f7c5af3e9dd495a2d33a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "eafb3b31ec7cebf556a529810d6f649a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isupporterrorinfo.dart", "hash": "16aa1ef738ce39b1d4a46e95bd8fdfd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersiststream.dart", "hash": "5f049e50ba97da4a4ead78fb7ff33dad"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "4591f6273e6282466c0364d5331e50c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.35/LICENSE", "hash": "e8b32b6d7c1328dfb1968caef8249452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactorymapping.dart", "hash": "4cbac1eea383d9069adc6a969f8f66d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiocaptureclient.dart", "hash": "fcb48a4f74d20f9d0767c13fadb819df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/traversable_monad_plus.dart", "hash": "73e72be55f1c14b5c3d325b53c794163"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechaudioformat.dart", "hash": "53ea2c5fd3a75125c1c5c2b4b323dd56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtreewalker.dart", "hash": "6ff721f374b943a1562c8e05dbcf814f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib/widgets.dart", "hash": "5cefcfd2b71c1471b6fe2693d74b7524"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/lib/src/fife.dart", "hash": "fea92554b4ff08ca6361ad4d226ddedb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/option.dart", "hash": "7dc4c768a268b1f328a5ae7938b8ba02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement5.dart", "hash": "c5f70755693110e17c5b6bf2e04d7a54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/unit.dart", "hash": "5197f5b02002982a07875a5c82d7f3bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/applicative_plus.dart", "hash": "d92cf8466e1b417df7128f4360e1b9d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "798f76b8076951e542aad4221a45d480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextchildpattern.dart", "hash": "b74f3bf94bc012c39c228948b2434413"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadataimport.dart", "hash": "d594c68b4bd289fec98c48d8753d4992"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "00dfe436d7f3546993ad86cc4f9ff655"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/sync_transformer.dart", "hash": "787074c3d370e068052721d16acefd9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "955794ab8f9f2f33f660998c73ac222f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/providers/auth_provider.dart", "hash": "0862478bbbfc0b8aeb19e678890cd05b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange3.dart", "hash": "4f4a2d291e23c96c7ae0d4dbc9598c54"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/entities/video.dart", "hash": "5878c3420572e6b640e01ba24f193c80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "2be9783170f41208ab65361d7cb0ddc4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/form_data.dart", "hash": "bfd57391197129cbe3c47c75b2c21672"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6f3424f2fc515abb888590b75c98e190"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "92137effa05660558f35cfc5845783bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immnotificationclient.dart", "hash": "6ef9470e2055a4adeffe2c757ce4073f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclock.dart", "hash": "889b2a51969dc04727672d045f214d9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwebauthenticationcoremanagerinterop.dart", "hash": "aef722a64f462b84d30dad6278040fb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dwmapi.g.dart", "hash": "07bef83d35c41f53d65fda935b414231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/tuple.dart", "hash": "41153b9605212032b0c116a0fedaed5f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "1b3814e3cd3f2d9543c7ebaf88384e10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtablepattern.dart", "hash": "acfd72d6ba5e1799364d54d9b5871d9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclockadjustment.dart", "hash": "87db5c50521a6f935c7dd4196eb44001"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_import.dart", "hash": "e0a5a25c69f7362ae3d6e493dfc611ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants_nodoc.dart", "hash": "161940e07ff4c31a1235e5ff9b16108e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "b9bfa2dc31960df2f1fd3aee88c3807e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_error_l1_1_0.g.dart", "hash": "892733d7fba692457dcd57fcd8e3dadd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "6b1f8660487a1ebca7109b1247c1c583"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "3e1bb909dcd21ccd8bdc03ba57bf02b2"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "594a02a84320cea18e06458c446d98e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestpackagedependency.dart", "hash": "2a81dde5f9a7f961c0f86199e56b5f88"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/headers.dart", "hash": "12ada90523ca5fc00e317c0a59889a1c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/firebase_core_platform_interface.dart", "hash": "a12b9a0771829ebdd5571928f9c48e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationsynchronizedinputpattern.dart", "hash": "01077b1240404868a99cdfb2c892cdff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/parameter.dart", "hash": "08b1358e505b0414dc60489b750ba2b6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/packages/fluttertoast/assets/toastify.css", "hash": "c4da0fc89c24df5cc6a180d9ac851706"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/guid.dart", "hash": "1699b455a9ecda569a7b35310ea98027"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/subscription/verify_payment_usecase.dart", "hash": "c1a8614bf5fe1d636e78187c3f37487a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatadispenserex.dart", "hash": "9817557f3ca542a625629bbc0bb2c6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation6.dart", "hash": "2a3155e741ae2ae06a4a13cea8b719a4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/development/flutter/bin/internal/engine.version", "hash": "32c4a9b6e6aa250ffd4aba05be285558"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_1.g.dart", "hash": "92268162591ef8477b2f93a31ce9758c"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/content/get_videos_usecase.dart", "hash": "18edac0326b152608784133e02af7896"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/datasources/auth_local_data_source.dart", "hash": "1e2536f558658031f07744fc412358ad"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "036fc28dc98388abec4456e8142c530f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/method_channel/method_channel_firebase_app.dart", "hash": "4f4575a514eec25990a9923547e2ac28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfile.dart", "hash": "bd68bf024d2e14c1e7fb775612927625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib/src/platform_view_player.dart", "hash": "95ab669f65ddc27eb1488b1e9038414e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ifiledialog.dart", "hash": "3ba0400a5592af52e69a5f66816ecae2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/equatable.dart", "hash": "1a5f064d497f9539e8e2cb4ba15a8f05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/istream.dart", "hash": "f336bda23f89f07f340a3e16b0406f05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/filters/development_filter.dart", "hash": "823ba102e678952071f18c5e6694d597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationinvokepattern.dart", "hash": "942a7879522bdf82258a3383893665a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "20d5458a880a0a10253cda660dbc42e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/user_model.dart", "hash": "d99b4f7c3c5686c3fc10407e4afa64aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "2c525c85cb323db613ddc5eba4b902d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/semigroup.dart", "hash": "567fe67f325c29a13be3d5389a8b0b82"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/LICENSE", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "hash": "87ee25bbef5b7cb7dcb056c3ec20f243"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "26312d25d45c45d94edcfbaaec9217b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/dual.dart", "hash": "917d4aea8cc9d304572210ad3984924e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "2d4b5a2778f275040b5e438045607332"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/category_model.g.dart", "hash": "9db826b8198e8a383ab2592f99304136"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionpattern.dart", "hash": "47221390638b312632445bde2ddc8b4d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/datasources/auth_remote_data_source.dart", "hash": "461af58c7eaf3923111b3d26a319ea15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retrofit-4.4.2/LICENSE", "hash": "eec8df566dadd4c873d9e323a7e31bd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/repositories/subscription_repository_impl.dart", "hash": "77d75980d54c87c9390dd9d77b8edacf"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/features_list.dart", "hash": "6f66f2a66e732eb8797b8d72d2da9649"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/monad_plus.dart", "hash": "ddd862a63899696b8a113d47b0abced1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "4eb84c94445470d8bb6bb8e2666aa51a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/gdi32.g.dart", "hash": "dacaaea99d06403132897f545645cd7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/evaluation.dart", "hash": "ed7800af26c6b91652549159cafa3221"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.5/LICENSE", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/rometadata.g.dart", "hash": "cd81c79fc585d5ed8858c3c530ec90f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersist.dart", "hash": "7e66e23bc10439da1ac0a1d84ef99ff3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib/src/messages.g.dart", "hash": "0abfcee62adc3e49034a318799a01997"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/exceptions.dart", "hash": "8bfe962047c432ca659d01097491e8b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "82ea4f7076bd7e32c383a2466518b943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "d953dedc9eee14dfb343f4c5988840c4"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/screens/new_screen.dart", "hash": "47c464313382c7a103dab8951f38b0b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "85e90b0b1f705d7db10d294017bcaf44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/auth/refresh_token_usecase.dart", "hash": "2e5bdee7f1a354af741a4bb8a1fdac0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "0a546a51fffe9612c8c3cbebc609691c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/monad_catch.dart", "hash": "223d01a7b297207382737d6178b388f1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/future.dart", "hash": "d08ab5c560b9b8e55fad9cf59db85f6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/immdeviceenumerator.dart", "hash": "40e923ff78f2c9cba09cb0652fd34e26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/uxtheme.g.dart", "hash": "c1b28c1b4b0d1008a417c0f6800696b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/constants_metadata.dart", "hash": "7a458ae2387a1d54657b7d330a7101e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/itypeinfo.dart", "hash": "5823785db5bc1f46ea00828058f2a973"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "bb020f793a10d8bb46c0bbc996bd0bfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "c86a43bc5abf7528416982490b4c0b8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/redirect_record.dart", "hash": "91794c215a8aa39b862cfa4c96b9a398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationproxyfactory.dart", "hash": "5d461db74d04d7e270d13a5a8a340796"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/error/exceptions.dart", "hash": "f78c992a64d6784073df93bf9d0666f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/constants/app_text_styles.dart", "hash": "8043a3fbce677d4fc68ff1fac6d5a2aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "1338341fe43eb21f20857cc392cf2f71"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiorenderclient.dart", "hash": "d32d5d6e1a22dea95ed2e78d62d2aff9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/.dart_tool/flutter_build/fe103b001158f569901686b21c60889d/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "cf5dc26d65244c12416f3411c6d79996"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/subscription/create_order_usecase.dart", "hash": "6342edd184917f711b1e8176ffa20d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "b24af65afbe06cb00d5661df3d3083af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessioncontrol.dart", "hash": "ec79f631eff71e86c32b832c1724df5f"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/packages/fluttertoast/assets/toastify.js", "hash": "56e2c9cedd97f10e7e5f1cebd85d53e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/platform_interface/platform_interface_firebase_app.dart", "hash": "209399f0e6f16675c3f087b8eb17087b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "6e320dd3d12f0e125541bc4b983dcfa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/src/types.dart", "hash": "1215aaf1f5de55c9669fd81e2046ca30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_handle_l1_1_0.g.dart", "hash": "02f24ac14177b434c3bebf0a9a2980db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/data/models/user_model.g.dart", "hash": "75590709b1952eff9c5b49bd88fcd0e7"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/constants/app_constants.dart", "hash": "7bea5f10a79c2304d174bb83e55a1ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "9b76b249fb23172215a62d66bb393ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextrange2.dart", "hash": "6905ddd5343384c6898473c3d0a553a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/avl_tree.dart", "hash": "df2c5a786d40515a3f6bb512f22a3751"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/lib/src/android_video_player.dart", "hash": "5a9e5d2d044f4f7bb99a3f8a703f4e04"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/multipart_file.dart", "hash": "4b7bd97845d5fc94f590ed6e58f1c1c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/applicative.dart", "hash": "bc82287e750a9051e60a4d5da3dd965b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/structs.dart", "hash": "b51cea8017e3cbb294fe3b8066265c7e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute_io.dart", "hash": "e990b24e6368a3aa33f21b4695cfcfab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "0a731b52181a917a08ac96b525f7d96b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/app.dart", "hash": "66bb0d42812dbdcb77a351f5d79c74a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_darwin.dart", "hash": "644e5e32abaad61eb192128f412424ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtableitempattern.dart", "hash": "983a75a70218eda5b2e68e97f3293f5d"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/content/mark_video_viewed_usecase.dart", "hash": "4c763df5016024f4701eb9e079c834b0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date.dart", "hash": "86b720af61fd71f6566c9e8d42412e85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_core_winrt_l1_1_0.g.dart", "hash": "5e93843d2c9e385658de36a256646d3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/app_bar_widget.dart", "hash": "467d91341f582df08b4d7ffa2450a6d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispellchecker2.dart", "hash": "28ceeb83a01abb867ca28f6c8b6e3e97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/method_channel/method_channel_firebase.dart", "hash": "a3239e1caa780c64918575ebdd5dd4cc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "ca759e06438affc7dcbdd9c4d8f0dbb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitem.dart", "hash": "450065123492ed697e9b82cdd6742580"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_mixin.dart", "hash": "5e9d885bc066ae16bcca5bf065c9d51f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/interceptors/imply_content_type.dart", "hash": "9955b767fdde0baa759d3431267e5ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/monad.dart", "hash": "e19fb27918acf8500b0c1184617023f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "ec0bf24485bc5f9b825a382457f586e2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eventify-1.0.1/LICENSE", "hash": "1112ce02e778f3cdd6243071b4dd304c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/variant.dart", "hash": "ea1d5b4167d55eb79626b0ad1bfe6a58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/traversable_monad.dart", "hash": "0c9bf8ada5a1123a9474b1b6167b28b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/firebase_exception.dart", "hash": "7cb7fe22378ec39b40d4b519d0928d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/src/platform_interface/platform_interface_firebase_plugin.dart", "hash": "07db573490cf88af2c2da7b393436779"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/lib/src/messages.g.dart", "hash": "9e38e7231aa3c753db67fd2be500ace9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "1ed34d373b037c1696e90bf7e4f249ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ibindctx.dart", "hash": "48b80cd162c0a5db716c6df6af6d33a3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationnotcondition.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/shell32.g.dart", "hash": "c313566bafbe45ee136f94f682b189fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/subscription_stream.dart", "hash": "b637f236939a0af5ddf1bae124669288"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/winmd_constants.dart", "hash": "16115596ace5bc18b10c61743655c625"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationgriditempattern.dart", "hash": "89cf0f2546a2003c136706514de87702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/netapi32.g.dart", "hash": "5f080ef031fd5b2c581f580bbdefd983"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "7f662c8207cea5db3d45f239a277ca9c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_ansi.dart", "hash": "d30eba29d046c1a8b7f029838de6e49f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/assets/toastify.js", "hash": "56e2c9cedd97f10e7e5f1cebd85d53e3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationlegacyiaccessiblepattern.dart", "hash": "43e95fe0932b68e415f370a7b5500cfd"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/core/constants/app_colors.dart", "hash": "a039a8f5638c33b739fedc4713a575b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishelllink.dart", "hash": "49be686324cf0d48dcb5a31b280affbe"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "d9dd226ec96aec60f125c0f1f8d00344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/int_to_hexstring.dart", "hash": "73cb6deeb88fdcc320cf8e089d51531d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/ntdll.g.dart", "hash": "b738d8d6cc15b84589aee0b3cabf344f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation5.dart", "hash": "d879c3156e19f2b290c4d6eed1de5e89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxfactory.dart", "hash": "93d835e43f33ca5ed96e6e85a392c1e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/compute/compute.dart", "hash": "12b8cbac25c7ad95ce53c2f8869a1b5d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/lib/src/avfoundation_video_player.dart", "hash": "bfdf6b2448cf5f47971ce50fb52c874c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/auth/google_sign_in_usecase.dart", "hash": "e59cec1c7faeb1f6d0f108fc2d22c236"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/task.dart", "hash": "9c1fbb2b5ccd138949c12b9c44f10185"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "083722b0880e8e5981f9e33da11e449c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "b33b1182e92dc3469db2563a33be2841"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/transform_empty_to_null.dart", "hash": "579bb0bd41c172690d80937bc1ce3b4c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/usecase.dart", "hash": "ea40ed7c1a541b2d60b3da530bd51285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationorcondition.dart", "hash": "4fade6330465dfa703dfe780444f8c7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/util/consolidate_bytes.dart", "hash": "b4446a7a4d053aaa35a7bc6968b4794a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechvoicestatus.dart", "hash": "0048b001d840547e4f9cd7be7fad486e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iconnectionpointcontainer.dart", "hash": "d3c3db414185f054f187734f6e35734e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "fcb9537911eb921d59812aded45a25ea"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienummoniker.dart", "hash": "43758553461b83b7bf6696d099d81613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/bstr.dart", "hash": "dad362de854994a72a95c2ddfb4f4dd6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/plus.dart", "hash": "0d6c9981d46850e2f6e6808c883badf6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "48b13baf494b39e894252da0a0f6e8c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "6b48e1348ae677efad30c0a9d4600e38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtexteditpattern.dart", "hash": "dec4d61e9b7fb95acd5269b1122ed1a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwinhttprequest.dart", "hash": "abcb8cf9c1f0bd0abc0fbf82868470fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_wsl_api_l1_1_0.g.dart", "hash": "cbe579638444243e204755d50bfeae50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/list.dart", "hash": "4e197f85e9ff9c4908bb9d72536b5716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemobjectaccess.dart", "hash": "2eb057b74241d39f07417866b331168e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "8e043971337ae96a1e56aaf2256540ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isensorcollection.dart", "hash": "c297d04053f831c4efb96b9e8dcafa0b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "4372cb3b63b820aff3fe67061bba3f9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/src/firebase_app.dart", "hash": "92822ea2edbf875f4f02ee0df917a511"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "e6f282a4b33b70c7d1d06bec39b155f8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "f30e48d0892af0c99b54816673cff9ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/src/flutter_secure_storage_windows_ffi.dart", "hash": "2ca4b9e92c39403717d2dcc32bed57e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapters/io_adapter.dart", "hash": "b7579897a220a029c3ea36d6d48b4144"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "45beeaf92542183f39c458a87dcc81f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/printers/pretty_printer.dart", "hash": "bf2bc3af52875d3e5715ed2dff220c07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "ebef4cfdfb854b138f6bdbbf53e73f0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/isimpleaudiovolume.dart", "hash": "fa2d84e94c7c5218924a785c41e40bb5"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/lib/shimmer.dart", "hash": "49d17d6f2c4de81e5dc0cf98d720333a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/winscard.g.dart", "hash": "58538b3b7601a27a2d6c24c2d74161b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemcontext.dart", "hash": "e060ba0218367e15110ec7001c8e07f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemlocator.dart", "hash": "8e0c72d73e1298cb4655ed8bfb6b9a37"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "2627dee7fb363a5bb1cbc919699bcc84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/transformers/background_transformer.dart", "hash": "c3ab437aa0b03081adbfcdff7755b358"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "dd12f205ac5895a2bdf7d059cc4b83b2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/services.dart", "hash": "bab8606629135509c96d78f7253526ed"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitem2.dart", "hash": "370b2d8885d48ae8d0316c6f0bb0ce05"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/widgets.dart", "hash": "9f9b1fcdf4037b3b4c71ed65b57e87f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumwbemclassobject.dart", "hash": "1480d297c2637befd67fbf59b4840a1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iunknown.dart", "hash": "71bc337251bdc832a7be27b05297de39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient.dart", "hash": "70ef104bba3a3ffb69b8ffe7b1baf6e1"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/adapter.dart", "hash": "80079ed73f37411d422a28fb563580bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/error.dart", "hash": "056ba78280a44883e05c65a88771b4e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "d7c9baf97f1348c00c56f8d64a3ce53a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/outputs/advanced_file_output.dart", "hash": "c00373b698001c5ecc9d5de61e542f5a"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/usecases/subscription/get_subscription_status_usecase.dart", "hash": "ba1ab3f64528e12e131ef173662b2844"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ienumvariant.dart", "hash": "d0247fa5a3aacccf4023fe5c01469e14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdragpattern.dart", "hash": "27ae743f7cf34ade61bc99eba4dedd81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/domain/repositories/subscription_repository.dart", "hash": "0e746e24fd19c15882c4875895694413"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/lib/razorpay_flutter.dart", "hash": "aeffb7688aa4e59e087aeb9831728718"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationpropertycondition.dart", "hash": "55b96cf000403acfb7c7d32447a4e102"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "53cf0d76bfd70bfdc7e2edb4a18327f4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "5b66a624b831dd9a7c94d59aaa10f8bb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/functor.dart", "hash": "21c771460f208edf2d1d7a27ca369a1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/lib/src/firebase.dart", "hash": "01049ee9adcd36bdba77ebdbf08c8311"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/api_ms_win_ro_typeresolution_l1_1_0.g.dart", "hash": "b9518d8d712e1322277d9f323e98281c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE", "hash": "ad4a5a1c16c771bac65521dacef3900e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/extensions/set_string.dart", "hash": "097e09840cc00325fdbebaacd05f4827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionpattern2.dart", "hash": "2066a7e06691045adc327c174ab0854f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/wlanapi.g.dart", "hash": "0d11940ff8ff1c4818bbf5978c55a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "d9a659644f1b667686f2c9b22545dc0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqlite_api.dart", "hash": "5494fe877262550facf407b379edae59"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/providers/content_provider.dart", "hash": "69082e319b26cee8f1750c5230d43730"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "ba2f8adc4e6c096b09aac919580fffee"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/lib/src/messages.g.dart", "hash": "081db414320a07c6aaa4c909dd276e78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_exception.dart", "hash": "2747964c64fe300f15d15123727cbcf6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a103dff72cbe4ef64a02c37dbfdc752d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/magnification.g.dart", "hash": "89a8b628ccf4c51b05b54a5cd9efccba"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationmultipleviewpattern.dart", "hash": "36e887f22fdae55fb0ea02599cd189f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechobjecttoken.dart", "hash": "28d0ea0e14e6f6d4fbc0784ea9e5b2ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationexpandcollapsepattern.dart", "hash": "109f9e8f02a0c411aead05d93b5258f9"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/subscription_card.dart", "hash": "72cefe8e0e3ef40e466d9ed19f0a39cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispeechbasestream.dart", "hash": "3cb4064cdabe9e96b0334d5877aafedf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.5.0/lib/src/filters/production_filter.dart", "hash": "5a28430581a3560d56d0014e44b89685"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "ec2260a55dbb3ff283297d9da97e130c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "ca3df05f249dbc5a38ebb86ee9a74a1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/io.dart", "hash": "cf356598bc8d7174deeaac4a5449a061"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iappxmanifestreader6.dart", "hash": "33186ffed4f0249b40a7d6161b7c2351"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ishellitemfilter.dart", "hash": "ade1e21aa46fef03f979ccd8866f11e7"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/imetadatatables2.dart", "hash": "f1f175eff474684786b1b6980f386aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/page.dart", "hash": "6b16a4d19243ba00762af7e39dafc177"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib/options/android_options.dart", "hash": "2d04b343ac3e272959ffa40b7b9d782c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.3.0/lib/video_player_platform_interface.dart", "hash": "c1c3f2403d5906a6e87fd8d2ecf14c04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomation4.dart", "hash": "5652ed8799ce8b441ea61e4baef419a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/lib/src/utils.dart", "hash": "04c394c41b5bc5c9c83ec859eae7205c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationselectionitempattern.dart", "hash": "2132997caebcefd4e7f00446c795cede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "b1884cfd8778cd71cea03ca8f4b39f4f"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "82d1200fedba087f85961d6b1b9332fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationtextpattern.dart", "hash": "b208f3c23955741947ead4a75ca0aa3c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iapplicationactivationmanager.dart", "hash": "c96999a0782dffe9bf8eeaf394caf3bd"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/lib/src/dio_mixin.dart", "hash": "e103c51878b3741ffe4d81896876f3ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient2.dart", "hash": "c8f773e164112ed7174c2a8ac494c222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "db6f70d83d36597cc6bc3eaaffd10aaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/idispatch.dart", "hash": "872b28cd09e937b0b3089f6c72cf310d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ispvoice.dart", "hash": "9a998f6a132477da93895bae423e3ac1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationspreadsheetpattern.dart", "hash": "fac91a50f448265e9a9f97994e8b529e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationelement.dart", "hash": "d1f3ee169c9fd7a9dec5eb03a4a33525"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "95f488b1875988eb094e0ba71deb7deb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/plus_empty.dart", "hash": "d6e68137f069ad2cf7c1fc96f4b33f07"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/lib/google_sign_in_android.dart", "hash": "6e5e6febf62ecdfd4bf02d622a7c6cf6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "e01f6851d87ad96cbdafcbfd282517e6"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/propsys.g.dart", "hash": "f21f9f81d251e1481096a9638691d07e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "9796b800122953ccb2c3f40ba2120a94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudioclient3.dart", "hash": "18b7345ba375180fbdb4bd38938f764d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "8f24c8ed1935c6f08997d0b9acb5bf38"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "db24bbb74875ecb216e8445bc10a0714"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib/flutter_secure_storage_windows.dart", "hash": "141745c6e29022622def8ba527cfd60c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iaudiosessionmanager.dart", "hash": "53ef1e482a9021fe353d68c9f8a1affc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/src/reentrant_lock.dart", "hash": "227fa2ff622f28df7ac1417cc0bbeed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE", "hash": "43465f3d93317f24a42a4f1dd5dc012e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/builtins.dart", "hash": "ff9e00e00e6436e6c885c34f8b22bbf5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "b3686e0781f3148d75a64ebb2bfef609"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationvaluepattern.dart", "hash": "3e61350adee7d79eed0c5d670bd6594e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/iphlpapi.g.dart", "hash": "603e877f67a065e3f92f62c54940b121"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "2c777edec67bbb084e5608fb5f6b495b"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iwbemservices.dart", "hash": "cad771ca5668c41d6aa03f3af838edd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iuiautomationdockpattern.dart", "hash": "70dffa2d7939cf32863e11a6560de9b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/logger/sqflite_logger.dart", "hash": "ab42e582c15854ab187bc7a07fb8baa5"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "505d7dde41bffe17b69e52db6ab37d0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/iinitializewithwindow.dart", "hash": "0748bf03bcf37edd1d571959e45a5cc0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "883b210f4cc20daebdb2834dbe4a512c"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "3d27bed38f1893769396b5d23f94f15e"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/comctl32.g.dart", "hash": "983eeb962574f2966f2fb4fb6f79af73"}, {"path": "/Users/<USER>/Documents/augment-projects/seekho-frontend/seekho_app/lib/presentation/widgets/category_grid.dart", "hash": "bbab95e08f5f991d3ce4002708ab9a07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/com/ipersistfile.dart", "hash": "90f6ddbbfda2bb5dd2ab2e05c777def0"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dartz-0.10.1/lib/src/iset.dart", "hash": "b33e045528bcb1d142dfe14e7134bd97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/lib/src/win32/dbghelp.g.dart", "hash": "740bd95d3579057bcb116d73ff96875d"}, {"path": "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "7c4df8be3ef1b8c4564f6aa3c64ba65d"}]}